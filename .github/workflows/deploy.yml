name: Deploy to VPS

on:
  push:
    branches:
      - main

permissions:
  contents: read
  packages: write

env:
  IMAGE_NAME: ghcr.io/sudocoder-py/b2b-outreach

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GHCR_TOKEN }}

      - name: Build Docker image
        run: docker build -t $IMAGE_NAME:latest .

      - name: Push Docker image
        run: docker push $IMAGE_NAME:latest

  deploy:
    runs-on: ubuntu-latest
    needs: build-and-push
    steps:
      # Deploy on the VPS
      - name: Deploy to VPS
        uses: appleboy/ssh-action@v0.1.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USER }}
          key: ${{ secrets.VPS_SSH_PRIVATE_KEY }}
          script: |
            echo "${{ secrets.GHCR_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin
            cd ~/projects/products/campaigns/b2b-cold-outreach &&
            docker-compose  -f docker-compose.prod.yml down &&
            docker-compose  -f docker-compose.prod.yml pull --quiet &&
            docker-compose  -f docker-compose.prod.yml up -d
