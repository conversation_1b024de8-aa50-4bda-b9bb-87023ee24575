
## AI placeholder issue: {leads_company} token not being replaced correctly in emails

## make option where we can make assigment msgs for all the campaign leads and all the campaign messages at once (we can use a for loop maybe?)

## responded content foor the lead?

## change the name of the tracking url to be less susbichous 
## add a hook for each one, like an email sending to my account using gmail, or notification in the app


## make production and staging and dev envs

## finnally make copy the admin css and templates edit them, then use them as frontend! 

## handle reply messages




# aug 7


### 4. implemnt the stats for http://127.0.0.1:8000/campaign/messages/ dealyed