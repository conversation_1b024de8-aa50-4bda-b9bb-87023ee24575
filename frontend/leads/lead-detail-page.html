<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f1f2f4] px-10 py-3">
          <div class="flex items-center gap-4 text-[#121417]">
            <div class="size-4">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M4 42.4379C4 42.4379 14.0962 36.0744 24 41.1692C35.0664 46.8624 44 42.2078 44 42.2078L44 7.01134C44 7.01134 35.068 11.6577 24.0031 5.96913C14.0971 0.876274 4 7.27094 4 7.27094L4 42.4379Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
            <h2 class="text-[#121417] text-lg font-bold leading-tight tracking-[-0.015em]">Campaign Automator</h2>
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <div class="flex items-center gap-9">
              <a class="text-[#121417] text-sm font-medium leading-normal" href="#">Dashboard</a>
              <a class="text-[#121417] text-sm font-medium leading-normal" href="#">Campaigns</a>
              <a class="text-[#121417] text-sm font-medium leading-normal" href="#">Leads</a>
              <a class="text-[#121417] text-sm font-medium leading-normal" href="#">Reports</a>
            </div>
            <button
              class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 bg-[#f1f2f4] text-[#121417] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
            >
              <div class="text-[#121417]" data-icon="Bell" data-size="20px" data-weight="regular">
                <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                  <path
                    d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"
                  ></path>
                </svg>
              </div>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuA3MVC6nwZsl6PKuGpNld2MPUnKTy4Z5Qw2EeB4j6eZgXtCGiiOdlOFkwX1_vR3oRQEJS0UhjN1kzZbVRml7174WHgY4k_ZCBBVjlumU_jF1isVqAIC6cTFhlCE_O2yJd_-4EmQq3VDi0KpEl3bRxd5YcZWJJ0qEyoA-BaYQCtTF2HMubd04zgsT0rEwD-OXPXiIX8buOJm074exfXB_4c4VlKPeWsz3wj7hL8LhQUIQ6HlMSMYk7KAOL51fye8MjrH_W7QY3qHOLg");'
            ></div>
          </div>
        </header>
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="flex flex-wrap gap-2 p-4">
              <a class="text-[#687682] text-base font-medium leading-normal" href="#">Leads</a>
              <span class="text-[#687682] text-base font-medium leading-normal">/</span>
              <span class="text-[#121417] text-base font-medium leading-normal">Sophia Clark</span>
            </div>
            <div class="flex flex-wrap justify-between gap-3 p-4">
              <div class="flex min-w-72 flex-col gap-3">
                <p class="text-[#121417] tracking-light text-[32px] font-bold leading-tight">Lead Details</p>
                <p class="text-[#687682] text-sm font-normal leading-normal">View and manage lead information</p>
              </div>
            </div>
            <h3 class="text-[#121417] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Lead Information</h3>
            <div class="p-4 grid grid-cols-2">
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pr-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Name</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">Sophia Clark</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pl-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Email</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">&lt;a href="mailto:<EMAIL>"&gt;<EMAIL>&lt;/a&gt;</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pr-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Phone</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">******-123-4567</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pl-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Company</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">Acme Corp</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pr-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Job Title</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">Marketing Manager</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pl-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Lead Source</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">Website Form</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pr-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Lead Status</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">Qualified</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pl-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Industry</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">Technology</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pr-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Location</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">San Francisco, CA</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pl-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Created At</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">2024-01-15</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pr-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Position</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">Manager</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pl-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">LinkedIn Profile</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">&lt;a href="https://www.linkedin.com/in/sophia-clark"&gt;LinkedIn Profile&lt;/a&gt;</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pr-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Company Website</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">&lt;a href="https://www.acmecorp.com"&gt;acmecorp.com&lt;/a&gt;</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pl-2">
                <p class="text-[#687682] text-sm font-normal leading-normal">Employee Count</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">1000+</p>
              </div>
              <div class="flex flex-col gap-1 border-t border-solid border-t-[#dde1e4] py-4 pr-2 col-span-2 pr-[50%]">
                <p class="text-[#687682] text-sm font-normal leading-normal">Company LinkedIn Page</p>
                <p class="text-[#121417] text-sm font-normal leading-normal">&lt;a href="https://www.linkedin.com/company/acmecorp"&gt;Acme Corp LinkedIn&lt;/a&gt;</p>
              </div>
            </div>
            <div class="flex justify-stretch">
              <div class="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-start">
                <button
                  class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#f1f2f4] text-[#121417] text-sm font-bold leading-normal tracking-[0.015em]"
                >
                  <span class="truncate">Edit Lead</span>
                </button>
                <button
                  class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-transparent text-[#121417] text-sm font-bold leading-normal tracking-[0.015em]"
                >
                  <span class="truncate">Delete Lead</span>
                </button>
              </div>
            </div>
            <div class="pb-3">
              <div class="flex border-b border-[#dde1e4] px-4 gap-8">
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-[#121417] text-[#121417] pb-[13px] pt-4" href="#">
                  <p class="text-[#121417] text-sm font-bold leading-normal tracking-[0.015em]">Campaign History</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#687682] pb-[13px] pt-4" href="#">
                  <p class="text-[#687682] text-sm font-bold leading-normal tracking-[0.015em]">Message History</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#687682] pb-[13px] pt-4" href="#">
                  <p class="text-[#687682] text-sm font-bold leading-normal tracking-[0.015em]">Newsletter Status</p>
                </a>
              </div>
            </div>
            <div class="flex flex-col px-4 py-6">
              <div class="flex flex-col items-center gap-6">
                <div
                  class="bg-center bg-no-repeat aspect-video bg-cover rounded-xl w-full max-w-[360px]"
                  style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBoH2k8758N457BntrQvAcWJyaCjrpIUwC_tbJQ2C7ZAydkJDIdQs_Haaqufud71gbLdu2BhD6vvF1w7vxX5nt79ypU61WyJcA9rN7-CDY8zF68EODJiK9eUmXvwhmBQovP70VDNRp4fN7CKXOCnFofJ9c01If_zW-s7oxhSOTAQTEyIp91XtoHVaSwTrkUv6Eo9j97ILG2QKxzNtZxntCcOBfmq6GZbIejhiyhHgwrgC9eBh3TPFc6zH_M_KhGFkxIZgd6DstECCs");'
                ></div>
                <div class="flex max-w-[480px] flex-col items-center gap-2">
                  <p class="text-[#121417] text-lg font-bold leading-tight tracking-[-0.015em] max-w-[480px] text-center">No Campaign History</p>
                  <p class="text-[#121417] text-sm font-normal leading-normal max-w-[480px] text-center">This lead has not been part of any campaigns yet.</p>
                </div>
              </div>
            </div>
            <div class="pb-3">
              <div class="flex border-b border-[#dde1e4] px-4 gap-8">
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#687682] pb-[13px] pt-4" href="#">
                  <p class="text-[#687682] text-sm font-bold leading-normal tracking-[0.015em]">Campaign History</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-[#121417] text-[#121417] pb-[13px] pt-4" href="#">
                  <p class="text-[#121417] text-sm font-bold leading-normal tracking-[0.015em]">Message History</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#687682] pb-[13px] pt-4" href="#">
                  <p class="text-[#687682] text-sm font-bold leading-normal tracking-[0.015em]">Newsletter Status</p>
                </a>
              </div>
            </div>
            <div class="flex flex-col px-4 py-6">
              <div class="flex flex-col items-center gap-6">
                <div
                  class="bg-center bg-no-repeat aspect-video bg-cover rounded-xl w-full max-w-[360px]"
                  style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuB_pZraS3qbJZc_XlV4vj4g0HaEW1RpbmyWRSSW4KUl3a8ZpygSiaDMfAWEU1vQ2dg2_BNbOBvfwhBXqbMsBv25K9nOJhGnCreiRBza2O0ny4KXAoeAampcuZGJrMt9Q-HD1ysRqYFb2UmRd9VdA3oUSFgmMmx4_QWyShh1XufZdqmUzy4YIyTLjfHUePcLBhljDMEDs3Yg0KClKay4aEW9MqleH2W6NCdw3suytZRKiiD7CdvfumGUQxTZVscHMakN0gk7a5iIBJc");'
                ></div>
                <div class="flex max-w-[480px] flex-col items-center gap-2">
                  <p class="text-[#121417] text-lg font-bold leading-tight tracking-[-0.015em] max-w-[480px] text-center">No Message History</p>
                  <p class="text-[#121417] text-sm font-normal leading-normal max-w-[480px] text-center">This lead has no message history.</p>
                </div>
              </div>
            </div>
            <div class="pb-3">
              <div class="flex border-b border-[#dde1e4] px-4 gap-8">
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#687682] pb-[13px] pt-4" href="#">
                  <p class="text-[#687682] text-sm font-bold leading-normal tracking-[0.015em]">Campaign History</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#687682] pb-[13px] pt-4" href="#">
                  <p class="text-[#687682] text-sm font-bold leading-normal tracking-[0.015em]">Message History</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-[#121417] text-[#121417] pb-[13px] pt-4" href="#">
                  <p class="text-[#121417] text-sm font-bold leading-normal tracking-[0.015em]">Newsletter Status</p>
                </a>
              </div>
            </div>
            <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 justify-between">
              <div class="flex flex-col justify-center">
                <p class="text-[#121417] text-base font-medium leading-normal line-clamp-1">Subscribed</p>
                <p class="text-[#687682] text-sm font-normal leading-normal line-clamp-2">Joined on 2024-01-15</p>
              </div>
              <div class="shrink-0">
                <label
                  class="relative flex h-[31px] w-[51px] cursor-pointer items-center rounded-full border-none bg-[#f1f2f4] p-0.5 has-[:checked]:justify-end has-[:checked]:bg-[#c3d9ee]"
                >
                  <div class="h-full w-[27px] rounded-full bg-white" style="box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 8px, rgba(0, 0, 0, 0.06) 0px 3px 1px;"></div>
                  <input type="checkbox" class="invisible absolute" />
                </label>
              </div>
            </div>
            <p class="text-[#687682] text-sm font-normal leading-normal pb-3 pt-1 px-4">Last updated: 2024-03-10</p>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
