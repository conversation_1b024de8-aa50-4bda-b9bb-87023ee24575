<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
          <div class="flex items-center gap-4 text-[#111518]">
            <div class="size-4">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M24 45.8096C19.6865 45.8096 15.4698 44.5305 11.8832 42.134C8.29667 39.7376 5.50128 36.3314 3.85056 32.3462C2.19985 28.361 1.76794 23.9758 2.60947 19.7452C3.451 15.5145 5.52816 11.6284 8.57829 8.5783C11.6284 5.52817 15.5145 3.45101 19.7452 2.60948C23.9758 1.76795 28.361 2.19986 32.3462 3.85057C36.3314 5.50129 39.7376 8.29668 42.134 11.8833C44.5305 15.4698 45.8096 19.6865 45.8096 24L24 24L24 45.8096Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
            <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">ConnectFlow</h2>
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <div class="flex items-center gap-9">
              <a class="text-[#111518] text-sm font-medium leading-normal" href="#">Dashboard</a>
              <a class="text-[#111518] text-sm font-medium leading-normal" href="#">Campaigns</a>
              <a class="text-[#111518] text-sm font-medium leading-normal" href="#">Leads</a>
              <a class="text-[#111518] text-sm font-medium leading-normal" href="#">Reports</a>
              <a class="text-[#111518] text-sm font-medium leading-normal" href="#">Settings</a>
            </div>
            <button
              class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 bg-[#f0f2f4] text-[#111518] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
            >
              <div class="text-[#111518]" data-icon="Question" data-size="20px" data-weight="regular">
                <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                  <path
                    d="M140,180a12,12,0,1,1-12-12A12,12,0,0,1,140,180ZM128,72c-22.06,0-40,16.15-40,36v4a8,8,0,0,0,16,0v-4c0-11,10.77-20,24-20s24,9,24,20-10.77,20-24,20a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-.72c18.24-3.35,32-17.9,32-35.28C168,88.15,150.06,72,128,72Zm104,56A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"
                  ></path>
                </svg>
              </div>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBAOw_1AlaeUTnTPdV9mlOvFxOnu_nNFmxiUy9lUelfZDlOO419-vH0L-oKKANR9VsEul27uxZ1IzpFlFKs5EQ_lYVslq62g_jJiET8lqyD60RMXCo4IEkw6s8pdcGRyGPg60v5ghkZUDKeJGmUXH7yzyYcX3NaDXqmTR0UV8XlRX9P9PAASypi8Imau-t0Only0LbHEvRhwRn_nAbF0TCp3Vz2z3fMqKG8wtwrpb4IDjNH7RqzNh6qluRgioMAU6nb1Y4EKINe_-Y");'
            ></div>
          </div>
        </header>
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="flex flex-wrap justify-between gap-3 p-4">
              <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight min-w-72">Import Leads from CSV</p>
            </div>
            <p class="text-[#111518] text-base font-normal leading-normal pb-3 pt-1 px-4">
              Upload a CSV file containing your leads. Ensure the file follows the required format for successful import.
            </p>
            <h3 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">CSV Format</h3>
            <p class="text-[#111518] text-base font-normal leading-normal pb-3 pt-1 px-4">Your CSV file should include the following columns in the specified order:</p>
            <div class="p-4 grid grid-cols-[20%_1fr] gap-x-6">
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#dbe1e6] py-5">
                <p class="text-[#617689] text-sm font-normal leading-normal">full_name</p>
                <p class="text-[#111518] text-sm font-normal leading-normal">Full Name</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#dbe1e6] py-5">
                <p class="text-[#617689] text-sm font-normal leading-normal">email</p>
                <p class="text-[#111518] text-sm font-normal leading-normal">Email Address</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#dbe1e6] py-5">
                <p class="text-[#617689] text-sm font-normal leading-normal">company_name</p>
                <p class="text-[#111518] text-sm font-normal leading-normal">Company Name</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#dbe1e6] py-5">
                <p class="text-[#617689] text-sm font-normal leading-normal">job_title</p>
                <p class="text-[#111518] text-sm font-normal leading-normal">Job Title</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#dbe1e6] py-5">
                <p class="text-[#617689] text-sm font-normal leading-normal">phone_number (Optional)</p>
                <p class="text-[#111518] text-sm font-normal leading-normal">Phone Number (e.g., ******-123-4567)</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#dbe1e6] py-5">
                <p class="text-[#617689] text-sm font-normal leading-normal">linkedin_url (Optional)</p>
                <p class="text-[#111518] text-sm font-normal leading-normal">LinkedIn Profile URL</p>
              </div>
            </div>
            <p class="text-[#617689] text-sm font-normal leading-normal pb-3 pt-1 px-4 underline">Download a sample CSV file to see the required format.</p>
            <div class="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
              <label class="flex flex-col min-w-40 flex-1">
                <p class="text-[#111518] text-base font-medium leading-normal pb-2">Upload CSV File (Max 10MB)</p>
                <input
                  placeholder="Choose a file"
                  class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111518] focus:outline-0 focus:ring-0 border border-[#dbe1e6] bg-white focus:border-[#dbe1e6] h-14 placeholder:text-[#617689] p-[15px] text-base font-normal leading-normal"
                  value=""
                />
              </label>
            </div>
            <div class="flex px-4 py-3 justify-end">
              <button
                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#2f92ee] text-white text-sm font-bold leading-normal tracking-[0.015em]"
              >
                <span class="truncate">Import Leads</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
