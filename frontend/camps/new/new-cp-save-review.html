<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-[#f8f9fc] group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#e6e9f4] px-10 py-3">
          <div class="flex items-center gap-4 text-[#0d0f1c]">
            <div class="size-4">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M42.4379 44C42.4379 44 36.0744 33.9038 41.1692 24C46.8624 12.9336 42.2078 4 42.2078 4L7.01134 4C7.01134 4 11.6577 12.932 5.96912 23.9969C0.876273 33.9029 7.27094 44 7.27094 44L42.4379 44Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
            <h2 class="text-[#0d0f1c] text-lg font-bold leading-tight tracking-[-0.015em]">CampaignPro</h2>
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <div class="flex items-center gap-9">
              <a class="text-[#0d0f1c] text-sm font-medium leading-normal" href="#">Dashboard</a>
              <a class="text-[#0d0f1c] text-sm font-medium leading-normal" href="#">Campaigns</a>
              <a class="text-[#0d0f1c] text-sm font-medium leading-normal" href="#">Leads</a>
              <a class="text-[#0d0f1c] text-sm font-medium leading-normal" href="#">Templates</a>
              <a class="text-[#0d0f1c] text-sm font-medium leading-normal" href="#">Reports</a>
            </div>
            <button
              class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 bg-[#e6e9f4] text-[#0d0f1c] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
            >
              <div class="text-[#0d0f1c]" data-icon="Bell" data-size="20px" data-weight="regular">
                <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                  <path
                    d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"
                  ></path>
                </svg>
              </div>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuA4y2wSiWCzL-fUg0IqbMolskDM7tn6sCSqa67WGeJAC1zbW6RI6omPfiLmdxTdWuelKDwLQxtNp7N52XV4D-JmiK-7xnw2iX3cv8KZvS3JNqXrVz6ZMRT_k23AokS8ipv53i-s4GmI-Y6FzyWodGyoL_oLOmazUdOJl4_WuxLW1JUIW6NN7x6_1WQ-eEdjrUHMq5lPOHO4Uy2931DWsSM5aEBG0ydw7muSyJcAyCXolsL9It1hf6Dh7u9-Hzu5y5FbgD1AbRcM35I");'
            ></div>
          </div>
        </header>
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="flex flex-wrap justify-between gap-3 p-4">
              <div class="flex min-w-72 flex-col gap-3">
                <p class="text-[#0d0f1c] tracking-light text-[32px] font-bold leading-tight">Create New Campaign</p>
                <p class="text-[#47579e] text-sm font-normal leading-normal">Follow the steps below to set up your campaign.</p>
              </div>
            </div>
            <div class="pb-3">
              <div class="flex border-b border-[#ced3e9] px-4 gap-8">
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#47579e] pb-[13px] pt-4" href="#">
                  <p class="text-[#47579e] text-sm font-bold leading-normal tracking-[0.015em]">Basic Info</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#47579e] pb-[13px] pt-4" href="#">
                  <p class="text-[#47579e] text-sm font-bold leading-normal tracking-[0.015em]">Select Leads</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#47579e] pb-[13px] pt-4" href="#">
                  <p class="text-[#47579e] text-sm font-bold leading-normal tracking-[0.015em]">Setup Messages</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-[#4264fa] text-[#0d0f1c] pb-[13px] pt-4" href="#">
                  <p class="text-[#0d0f1c] text-sm font-bold leading-normal tracking-[0.015em]">Review &amp; Save</p>
                </a>
              </div>
            </div>
            <h2 class="text-[#0d0f1c] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Step 4: Review &amp; Save</h2>
            <div class="p-4 grid grid-cols-[20%_1fr] gap-x-6">
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#ced3e9] py-5">
                <p class="text-[#47579e] text-sm font-normal leading-normal">Campaign Name</p>
                <p class="text-[#0d0f1c] text-sm font-normal leading-normal">Summer Sale 2024</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#ced3e9] py-5">
                <p class="text-[#47579e] text-sm font-normal leading-normal">Selected Product</p>
                <p class="text-[#0d0f1c] text-sm font-normal leading-normal">T-shirts</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#ced3e9] py-5">
                <p class="text-[#47579e] text-sm font-normal leading-normal">Date Range</p>
                <p class="text-[#0d0f1c] text-sm font-normal leading-normal">July 1, 2024 - August 31, 2024</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#ced3e9] py-5">
                <p class="text-[#47579e] text-sm font-normal leading-normal">Number of Leads Selected</p>
                <p class="text-[#0d0f1c] text-sm font-normal leading-normal">1500</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#ced3e9] py-5">
                <p class="text-[#47579e] text-sm font-normal leading-normal">Message Templates Selected</p>
                <p class="text-[#0d0f1c] text-sm font-normal leading-normal">Welcome Email, Newsletter</p>
              </div>
            </div>
            <div class="flex px-4 py-3 justify-end">
              <button
                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#4264fa] text-[#f8f9fc] text-sm font-bold leading-normal tracking-[0.015em]"
              >
                <span class="truncate">Save Campaign</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
