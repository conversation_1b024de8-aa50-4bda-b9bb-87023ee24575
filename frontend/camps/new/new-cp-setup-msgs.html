<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-[#f9f9fb] group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#e9eaf2] px-10 py-3">
          <div class="flex items-center gap-4 text-[#0f111a]">
            <div class="size-4">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M42.4379 44C42.4379 44 36.0744 33.9038 41.1692 24C46.8624 12.9336 42.2078 4 42.2078 4L7.01134 4C7.01134 4 11.6577 12.932 5.96912 23.9969C0.876273 33.9029 7.27094 44 7.27094 44L42.4379 44Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
            <h2 class="text-[#0f111a] text-lg font-bold leading-tight tracking-[-0.015em]">CampaignPro</h2>
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <div class="flex items-center gap-9">
              <a class="text-[#0f111a] text-sm font-medium leading-normal" href="#">Dashboard</a>
              <a class="text-[#0f111a] text-sm font-medium leading-normal" href="#">Campaigns</a>
              <a class="text-[#0f111a] text-sm font-medium leading-normal" href="#">Leads</a>
              <a class="text-[#0f111a] text-sm font-medium leading-normal" href="#">Templates</a>
              <a class="text-[#0f111a] text-sm font-medium leading-normal" href="#">Reports</a>
            </div>
            <button
              class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 bg-[#e9eaf2] text-[#0f111a] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
            >
              <div class="text-[#0f111a]" data-icon="Bell" data-size="20px" data-weight="regular">
                <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                  <path
                    d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"
                  ></path>
                </svg>
              </div>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuA4y2wSiWCzL-fUg0IqbMolskDM7tn6sCSqa67WGeJAC1zbW6RI6omPfiLmdxTdWuelKDwLQxtNp7N52XV4D-JmiK-7xnw2iX3cv8KZvS3JNqXrVz6ZMRT_k23AokS8ipv53i-s4GmI-Y6FzyWodGyoL_oLOmazUdOJl4_WuxLW1JUIW6NN7x6_1WQ-eEdjrUHMq5lPOHO4Uy2931DWsSM5aEBG0ydw7muSyJcAyCXolsL9It1hf6Dh7u9-Hzu5y5FbgD1AbRcM35I");'
            ></div>
          </div>
        </header>
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="flex flex-wrap justify-between gap-3 p-4">
              <div class="flex min-w-72 flex-col gap-3">
                <p class="text-[#0f111a] tracking-light text-[32px] font-bold leading-tight">Create New Campaign</p>
                <p class="text-[#56618f] text-sm font-normal leading-normal">Follow the steps below to set up your campaign.</p>
              </div>
            </div>
            <div class="pb-3">
              <div class="flex border-b border-[#d2d6e4] px-4 gap-8">
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#56618f] pb-[13px] pt-4" href="#">
                  <p class="text-[#56618f] text-sm font-bold leading-normal tracking-[0.015em]">Basic Info</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#56618f] pb-[13px] pt-4" href="#">
                  <p class="text-[#56618f] text-sm font-bold leading-normal tracking-[0.015em]">Select Leads</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-[#eaedfa] text-[#0f111a] pb-[13px] pt-4" href="#">
                  <p class="text-[#0f111a] text-sm font-bold leading-normal tracking-[0.015em]">Setup Messages</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#56618f] pb-[13px] pt-4" href="#">
                  <p class="text-[#56618f] text-sm font-bold leading-normal tracking-[0.015em]">Review &amp; Save</p>
                </a>
              </div>
            </div>
            <h2 class="text-[#0f111a] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Step 3: Setup Messages</h2>
            <div class="px-4 py-3">
              <label class="flex flex-col min-w-40 h-12 w-full">
                <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                  <div
                    class="text-[#56618f] flex border-none bg-[#e9eaf2] items-center justify-center pl-4 rounded-l-xl border-r-0"
                    data-icon="MagnifyingGlass"
                    data-size="24px"
                    data-weight="regular"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                      ></path>
                    </svg>
                  </div>
                  <input
                    placeholder="Search templates..."
                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#0f111a] focus:outline-0 focus:ring-0 border-none bg-[#e9eaf2] focus:border-none h-full placeholder:text-[#56618f] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                    value=""
                  />
                </div>
              </label>
            </div>
            <div class="flex gap-3 p-3 flex-wrap pr-4">
              <div class="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#e9eaf2] pl-4 pr-4">
                <p class="text-[#0f111a] text-sm font-medium leading-normal">Category</p>
              </div>
              <div class="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#e9eaf2] pl-4 pr-4">
                <p class="text-[#0f111a] text-sm font-medium leading-normal">Type</p>
              </div>
              <div class="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#e9eaf2] pl-4 pr-4">
                <p class="text-[#0f111a] text-sm font-medium leading-normal">Date Created</p>
              </div>
            </div>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#d2d6e4] bg-[#f9f9fb]">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-[#f9f9fb]">
                      <th class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-120 px-4 py-3 text-left text-[#0f111a] text-sm font-medium leading-normal">Select</th>
                      <th class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-240 px-4 py-3 text-left text-[#0f111a] w-[400px] text-sm font-medium leading-normal">
                        Template Name
                      </th>
                      <th class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-360 px-4 py-3 text-left text-[#0f111a] w-60 text-sm font-medium leading-normal">Category</th>
                      <th class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-480 px-4 py-3 text-left text-[#0f111a] w-60 text-sm font-medium leading-normal">Type</th>
                      <th class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-600 px-4 py-3 text-left text-[#0f111a] w-[400px] text-sm font-medium leading-normal">
                        Date Created
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#d2d6e4]">
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-120 h-[72px] px-4 py-2 text-sm font-normal leading-normal">true</td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-240 h-[72px] px-4 py-2 w-[400px] text-[#0f111a] text-sm font-normal leading-normal">
                        Welcome Email
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-360 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Onboarding</span>
                        </button>
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Email</span>
                        </button>
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-600 h-[72px] px-4 py-2 w-[400px] text-[#56618f] text-sm font-normal leading-normal">
                        2024-01-20
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d2d6e4]">
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-120 h-[72px] px-4 py-2 text-sm font-normal leading-normal">false</td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-240 h-[72px] px-4 py-2 w-[400px] text-[#0f111a] text-sm font-normal leading-normal">
                        Follow-up SMS
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-360 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Sales</span>
                        </button>
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">SMS</span>
                        </button>
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-600 h-[72px] px-4 py-2 w-[400px] text-[#56618f] text-sm font-normal leading-normal">
                        2024-02-15
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d2d6e4]">
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-120 h-[72px] px-4 py-2 text-sm font-normal leading-normal">true</td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-240 h-[72px] px-4 py-2 w-[400px] text-[#0f111a] text-sm font-normal leading-normal">
                        Newsletter
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-360 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Marketing</span>
                        </button>
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Email</span>
                        </button>
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-600 h-[72px] px-4 py-2 w-[400px] text-[#56618f] text-sm font-normal leading-normal">
                        2024-03-05
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d2d6e4]">
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-120 h-[72px] px-4 py-2 text-sm font-normal leading-normal">false</td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-240 h-[72px] px-4 py-2 w-[400px] text-[#0f111a] text-sm font-normal leading-normal">
                        Reminder SMS
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-360 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Support</span>
                        </button>
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">SMS</span>
                        </button>
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-600 h-[72px] px-4 py-2 w-[400px] text-[#56618f] text-sm font-normal leading-normal">
                        2024-04-10
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d2d6e4]">
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-120 h-[72px] px-4 py-2 text-sm font-normal leading-normal">true</td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-240 h-[72px] px-4 py-2 w-[400px] text-[#0f111a] text-sm font-normal leading-normal">
                        Feedback Request
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-360 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Customer Service</span>
                        </button>
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Email</span>
                        </button>
                      </td>
                      <td class="table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-600 h-[72px] px-4 py-2 w-[400px] text-[#56618f] text-sm font-normal leading-normal">
                        2024-05-22
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-120{display: none;}}
                @container(max-width:240px){.table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-240{display: none;}}
                @container(max-width:360px){.table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-360{display: none;}}
                @container(max-width:480px){.table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-480{display: none;}}
                @container(max-width:600px){.table-c21fb029-c303-4c30-b3f2-4a52160de6df-column-600{display: none;}}
              </style>
            </div>
            <div class="flex justify-stretch">
              <div class="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-start">
                <button
                  class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-bold leading-normal tracking-[0.015em]"
                >
                  <span class="truncate">Select All Visible</span>
                </button>
                <button
                  class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-bold leading-normal tracking-[0.015em]"
                >
                  <span class="truncate">Deselect All</span>
                </button>
              </div>
            </div>
            <p class="text-[#56618f] text-sm font-normal leading-normal pb-3 pt-1 px-4">3 templates selected</p>
            <div class="flex px-4 py-3 justify-start">
              <button
                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e9eaf2] text-[#0f111a] text-sm font-bold leading-normal tracking-[0.015em]"
              >
                <span class="truncate">Add New Template to this Campaign</span>
              </button>
            </div>
            <div class="flex items-center justify-center p-4">
              <a href="#" class="flex size-10 items-center justify-center">
                <div class="text-[#0f111a]" data-icon="CaretLeft" data-size="18px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18px" height="18px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M165.66,202.34a8,8,0,0,1-11.32,11.32l-80-80a8,8,0,0,1,0-11.32l80-80a8,8,0,0,1,11.32,11.32L91.31,128Z"></path>
                  </svg>
                </div>
              </a>
              <a class="text-sm font-bold leading-normal tracking-[0.015em] flex size-10 items-center justify-center text-[#0f111a] rounded-full bg-[#e9eaf2]" href="#">1</a>
              <a class="text-sm font-normal leading-normal flex size-10 items-center justify-center text-[#0f111a] rounded-full" href="#">2</a>
              <a class="text-sm font-normal leading-normal flex size-10 items-center justify-center text-[#0f111a] rounded-full" href="#">3</a>
              <span class="text-sm font-normal leading-normal flex size-10 items-center justify-center text-[#0f111a] rounded-full" href="#">...</span>
              <a class="text-sm font-normal leading-normal flex size-10 items-center justify-center text-[#0f111a] rounded-full" href="#">10</a>
              <a href="#" class="flex size-10 items-center justify-center">
                <div class="text-[#0f111a]" data-icon="CaretRight" data-size="18px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18px" height="18px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                  </svg>
                </div>
              </a>
            </div>
            <div class="flex px-4 py-3 justify-end">
              <button
                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#eaedfa] text-[#0f111a] text-sm font-bold leading-normal tracking-[0.015em]"
              >
                <span class="truncate">Next: Review &amp; Save</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
