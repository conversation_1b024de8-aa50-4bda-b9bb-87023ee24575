<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-[#f8f9fc] group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#e6e9f4] px-10 py-3">
          <div class="flex items-center gap-8">
            <div class="flex items-center gap-4 text-[#0d0f1c]">
              <div class="size-4">
                <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 6H42L36 24L42 42H6L12 24L6 6Z" fill="currentColor"></path></svg>
              </div>
              <h2 class="text-[#0d0f1c] text-lg font-bold leading-tight tracking-[-0.015em]">Growth Engine</h2>
            </div>
            <div class="flex items-center gap-9">
              <a class="text-[#0d0f1c] text-sm font-medium leading-normal" href="#">Dashboard</a>
              <a class="text-[#0d0f1c] text-sm font-medium leading-normal" href="#">Campaigns</a>
              <a class="text-[#0d0f1c] text-sm font-medium leading-normal" href="#">Audiences</a>
              <a class="text-[#0d0f1c] text-sm font-medium leading-normal" href="#">Experiments</a>
              <a class="text-[#0d0f1c] text-sm font-medium leading-normal" href="#">Reports</a>
            </div>
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <label class="flex flex-col min-w-40 !h-10 max-w-64">
              <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                <div
                  class="text-[#47569e] flex border-none bg-[#e6e9f4] items-center justify-center pl-4 rounded-l-xl border-r-0"
                  data-icon="MagnifyingGlass"
                  data-size="24px"
                  data-weight="regular"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                    ></path>
                  </svg>
                </div>
                <input
                  placeholder="Search"
                  class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#0d0f1c] focus:outline-0 focus:ring-0 border-none bg-[#e6e9f4] focus:border-none h-full placeholder:text-[#47569e] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                  value=""
                />
              </div>
            </label>
            <button
              class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e6e9f4] text-[#0d0f1c] text-sm font-bold leading-normal tracking-[0.015em]"
            >
              <span class="truncate">Invite team</span>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAj6qw0NTJZvNYKCTeaYYC5LDU2DELslfsqbLXrHj-iUKgnGwaLLa0sa8kqbvPk897yRu0mBqzE8TwqkuredtEgWp3zMUpq2KCjGXuKWQUAF6YPGl2G0jL1gkSa5zlthSnTyzZ1MMeg1Auhce-H5M88Ur7aRc6PdYk4vJymi_1W7zX4MbhpFX7aOHZ2J6uOkQ-X7qj_L-AevWHPKml84mMaXc5Muku-XlHPAr0lC0pHgaHn2_OLrRiP4xyBonRiyRhYHk2VF4Veck0");'
            ></div>
          </div>
        </header>
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="flex flex-wrap justify-between gap-3 p-4">
              <p class="text-[#0d0f1c] tracking-light text-[32px] font-bold leading-tight min-w-72">Campaigns</p>
              <button
                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e6e9f4] text-[#0d0f1c] text-sm font-medium leading-normal"
              >
                <span class="truncate">Create New Campaign</span>
              </button>
            </div>
            <div class="px-4 py-3">
              <label class="flex flex-col min-w-40 h-12 w-full">
                <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                  <div
                    class="text-[#47569e] flex border-none bg-[#e6e9f4] items-center justify-center pl-4 rounded-l-xl border-r-0"
                    data-icon="MagnifyingGlass"
                    data-size="24px"
                    data-weight="regular"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                      ></path>
                    </svg>
                  </div>
                  <input
                    placeholder="Search campaigns"
                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#0d0f1c] focus:outline-0 focus:ring-0 border-none bg-[#e6e9f4] focus:border-none h-full placeholder:text-[#47569e] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                    value=""
                  />
                </div>
              </label>
            </div>
            <div class="flex gap-3 p-3 flex-wrap pr-4">
              <button class="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#e6e9f4] pl-4 pr-2">
                <p class="text-[#0d0f1c] text-sm font-medium leading-normal">Product</p>
                <div class="text-[#0d0f1c]" data-icon="CaretDown" data-size="20px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <button class="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#e6e9f4] pl-4 pr-2">
                <p class="text-[#0d0f1c] text-sm font-medium leading-normal">Status</p>
                <div class="text-[#0d0f1c]" data-icon="CaretDown" data-size="20px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <button class="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#e6e9f4] pl-4 pr-2">
                <p class="text-[#0d0f1c] text-sm font-medium leading-normal">Date Range</p>
                <div class="text-[#0d0f1c]" data-icon="CaretDown" data-size="20px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
            </div>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#ced2e9] bg-[#f8f9fc]">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-[#f8f9fc]">
                      <th class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-120 px-4 py-3 text-left text-[#0d0f1c] w-[400px] text-sm font-medium leading-normal">
                        Campaign Name
                      </th>
                      <th class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-240 px-4 py-3 text-left text-[#0d0f1c] w-[400px] text-sm font-medium leading-normal">
                        Product Name
                      </th>
                      <th class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-360 px-4 py-3 text-left text-[#0d0f1c] w-[400px] text-sm font-medium leading-normal">
                        Start Date
                      </th>
                      <th class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-480 px-4 py-3 text-left text-[#0d0f1c] w-[400px] text-sm font-medium leading-normal">
                        End Date
                      </th>
                      <th class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-600 px-4 py-3 text-left text-[#0d0f1c] w-60 text-sm font-medium leading-normal">Status</th>
                      <th class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-720 px-4 py-3 text-left text-[#0d0f1c] w-[400px] text-sm font-medium leading-normal">
                        Key Stats
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#ced2e9]">
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-120 h-[72px] px-4 py-2 w-[400px] text-[#0d0f1c] text-sm font-normal leading-normal">
                        Summer Sale
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-240 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">Apparel</td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-360 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        06/01/2024
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-480 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        08/31/2024
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-600 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e6e9f4] text-[#0d0f1c] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Active</span>
                        </button>
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-720 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        150 Leads, 10% Conversion
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-840 h-[72px] px-4 py-2 w-60 text-[#47569e] text-sm font-bold leading-normal tracking-[0.015em]">
                        View Campaign Dashboard, Edit, Delete
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#ced2e9]">
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-120 h-[72px] px-4 py-2 w-[400px] text-[#0d0f1c] text-sm font-normal leading-normal">
                        Back to School
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-240 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        Stationery
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-360 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        08/15/2024
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-480 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        09/30/2024
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-600 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e6e9f4] text-[#0d0f1c] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Active</span>
                        </button>
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-720 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        200 Leads, 15% Conversion
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-840 h-[72px] px-4 py-2 w-60 text-[#47569e] text-sm font-bold leading-normal tracking-[0.015em]">
                        View Campaign Dashboard, Edit, Delete
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#ced2e9]">
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-120 h-[72px] px-4 py-2 w-[400px] text-[#0d0f1c] text-sm font-normal leading-normal">
                        Holiday Promotion
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-240 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        Electronics
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-360 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        11/01/2024
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-480 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        12/31/2024
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-600 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e6e9f4] text-[#0d0f1c] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Inactive</span>
                        </button>
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-720 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        50 Leads, 5% Conversion
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-840 h-[72px] px-4 py-2 w-60 text-[#47569e] text-sm font-bold leading-normal tracking-[0.015em]">
                        View Campaign Dashboard, Edit, Delete
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#ced2e9]">
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-120 h-[72px] px-4 py-2 w-[400px] text-[#0d0f1c] text-sm font-normal leading-normal">
                        Spring Collection
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-240 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        Accessories
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-360 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        03/01/2024
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-480 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        05/31/2024
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-600 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e6e9f4] text-[#0d0f1c] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Active</span>
                        </button>
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-720 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        100 Leads, 8% Conversion
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-840 h-[72px] px-4 py-2 w-60 text-[#47569e] text-sm font-bold leading-normal tracking-[0.015em]">
                        View Campaign Dashboard, Edit, Delete
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#ced2e9]">
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-120 h-[72px] px-4 py-2 w-[400px] text-[#0d0f1c] text-sm font-normal leading-normal">
                        Winter Clearance
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-240 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">Footwear</td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-360 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        01/01/2024
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-480 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        02/28/2024
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-600 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e6e9f4] text-[#0d0f1c] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Inactive</span>
                        </button>
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-720 h-[72px] px-4 py-2 w-[400px] text-[#47569e] text-sm font-normal leading-normal">
                        75 Leads, 7% Conversion
                      </td>
                      <td class="table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-840 h-[72px] px-4 py-2 w-60 text-[#47569e] text-sm font-bold leading-normal tracking-[0.015em]">
                        View Campaign Dashboard, Edit, Delete
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-120{display: none;}}
                @container(max-width:240px){.table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-240{display: none;}}
                @container(max-width:360px){.table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-360{display: none;}}
                @container(max-width:480px){.table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-480{display: none;}}
                @container(max-width:600px){.table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-600{display: none;}}
                @container(max-width:720px){.table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-720{display: none;}}
                @container(max-width:840px){.table-4c49460c-4e83-40d1-af6c-4a932f7d5fb9-column-840{display: none;}}
              </style>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
