#!/usr/bin/env python
"""
Simple test script to verify login integration
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append('/home/<USER>/Desktop/cccrm')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dcrm.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from clients.models import CustomUser, SubscribedCompany

def test_login_integration():
    """Test the login integration"""
    print("Testing login integration...")
    
    # Create a test client
    client = Client()
    
    # Test 1: Check if login page loads
    print("\n1. Testing login page load...")
    try:
        response = client.get(reverse('login'))
        if response.status_code == 200:
            print("✓ Login page loads successfully")
        else:
            print(f"✗ Login page failed to load: {response.status_code}")
    except Exception as e:
        print(f"✗ Error loading login page: {e}")
    
    # Test 2: Check if API login endpoint exists
    print("\n2. Testing API login endpoint...")
    try:
        response = client.post(reverse('api_login'), {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        })
        if response.status_code in [400, 401]:  # Expected for wrong credentials
            print("✓ API login endpoint is accessible")
        else:
            print(f"✗ Unexpected response from API login: {response.status_code}")
    except Exception as e:
        print(f"✗ Error accessing API login: {e}")
    
    # Test 3: Check if products URL exists (redirect target)
    print("\n3. Testing products page (redirect target)...")
    try:
        response = client.get(reverse('products'))
        # This might redirect to login if not authenticated, which is expected
        if response.status_code in [200, 302]:
            print("✓ Products page is accessible")
        else:
            print(f"✗ Products page error: {response.status_code}")
    except Exception as e:
        print(f"✗ Error accessing products page: {e}")
    
    print("\n" + "="*50)
    print("Login integration test completed!")
    print("="*50)

if __name__ == "__main__":
    test_login_integration()
