#!/usr/bin/env python
"""
Simple test script to verify traditional login integration
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append('/home/<USER>/Desktop/cccrm')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dcrm.settings')
django.setup()

from django.test import Client
from django.urls import reverse

def test_traditional_login():
    """Test the traditional login integration"""
    print("Testing traditional login integration...")
    
    # Create a test client
    client = Client()
    
    # Test 1: Check if login page loads
    print("\n1. Testing login page load...")
    try:
        response = client.get(reverse('login'))
        if response.status_code == 200:
            print("✓ Login page loads successfully")
            # Check if the form has the correct action
            if 'login/submit/' in response.content.decode():
                print("✓ Form action points to correct URL")
            else:
                print("✗ Form action might be incorrect")
        else:
            print(f"✗ Login page failed to load: {response.status_code}")
    except Exception as e:
        print(f"✗ Error loading login page: {e}")
    
    # Test 2: Check if login submit endpoint exists
    print("\n2. Testing login submit endpoint...")
    try:
        response = client.post(reverse('login_submit'), {
            'username': 'testuser',
            'password': 'wrongpassword'
        })
        if response.status_code in [200, 302]:  # 200 for error, 302 for redirect
            print("✓ Login submit endpoint is accessible")
        else:
            print(f"✗ Unexpected response from login submit: {response.status_code}")
    except Exception as e:
        print(f"✗ Error accessing login submit: {e}")
    
    # Test 3: Check if products URL exists (redirect target)
    print("\n3. Testing products page (redirect target)...")
    try:
        response = client.get(reverse('products'))
        if response.status_code in [200, 302]:
            print("✓ Products page is accessible")
        else:
            print(f"✗ Products page error: {response.status_code}")
    except Exception as e:
        print(f"✗ Error accessing products page: {e}")
    
    print("\n" + "="*50)
    print("Traditional login integration test completed!")
    print("="*50)

if __name__ == "__main__":
    test_traditional_login()
