<svg width="362" height="99" viewBox="0 0 362 99" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 1. The gradient from the example SVG -->
    <linearGradient id="vibeGradient" x1="0%" y1="100%" x2="0%" y2="0%">
      <stop offset="0%" stop-color="rgba(82, 32, 3, 1)" />
      <stop offset="35%" stop-color="rgba(153, 109, 8, 1)" />
      <stop offset="100%" stop-color="rgba(255, 64, 0, 1)" />
    </linearGradient>

    <!-- 2. The shape from the original image -->
    <g id="vibeOriginalShape">
      <!-- V -->
      <path fill-rule="evenodd" d="M49.5 99L0 0H99L49.5 99ZM49.5 68.2L72.6 22H26.4L49.5 68.2Z"/>
      <!-- I -->
      <path d="M108.9 0H140.8V99H108.9V0Z"/>
      <!-- B -->
      <path d="M150.7 0H210.1C243.1 0 243.1 40.7 210.1 40.7H182.6V58.3H219.45C252.45 58.3 252.45 99 219.45 99H150.7V0Z"/>
      <!-- E -->
      <path d="M264 0H354V25.3H264V0ZM264 36.8H354V62.1H264V36.8ZM264 73.7H354V99H264V73.7Z"/>
    </g>
  </defs>

  <!-- 3. The layering technique from the example SVG -->

  <!-- Back layer (dark, shifted left) -->
  <use href="#vibeOriginalShape" fill="rgba(82, 32, 3, 1)" transform="translate(-4, 0)"/>
  
  <!-- Front layer (bright, shifted right) -->
  <use href="#vibeOriginalShape" fill="rgba(255, 64, 0, 1)" transform="translate(4, 0)"/>
  
  <!-- Main layer (gradient, centered) -->
  <use href="#vibeOriginalShape" fill="url(#vibeGradient)"/>
</svg>