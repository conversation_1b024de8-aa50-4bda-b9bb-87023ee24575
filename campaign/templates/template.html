let isInteractionsBlocked = false;
let tooltip = null;
let blurStylesInjected = false;

// Inject CSS for blur effect
function injectBlurStyles() {
    if (blurStylesInjected) return;
    
    const style = document.createElement('style');
    style.textContent = `
        .interaction-blocked-blur {
            filter: blur(2px);
            transition: filter 0.3s ease;
        }
        .interaction-blocked-blur:hover {
            filter: blur(2px);
        }
    `;
    document.head.appendChild(style);
    blurStylesInjected = true;
}

// Create the tooltip element
function createTooltip() {
    tooltip = document.createElement('div');
    tooltip.textContent = 'You cannot interact with the page right now';
    tooltip.style.cssText = `
        position: fixed;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        pointer-events: none;
        z-index: 10000;
        display: none;
        transition: opacity 0.3s;
    `;
    document.body.appendChild(tooltip);
}

// Show tooltip at position
function showTooltip(x, y) {
    if (!tooltip) createTooltip();
    
    tooltip.style.left = `${x}px`;
    tooltip.style.top = `${y}px`;
    tooltip.style.display = 'block';
    
    // Hide after 2 seconds
    setTimeout(() => {
        tooltip.style.display = 'none';
    }, 2000);
}

// Handle interaction attempts with blur effect
function handleInteraction(event) {
    if (!isInteractionsBlocked) return;
    
    // Check if target is an interactive element
    const interactiveElement = event.target.closest(
        'button, input, select, textarea, a, [role="button"], [tabindex]:not([tabindex="-1"])'
    );
    
    if (interactiveElement) {
        event.preventDefault();
        event.stopPropagation();
        
        // Add blur effect
        interactiveElement.classList.add('interaction-blocked-blur');
        
        // Remove blur effect after animation
        setTimeout(() => {
            interactiveElement.classList.remove('interaction-blocked-blur');
        }, 500);
        
        // Show tooltip at cursor position
        showTooltip(event.clientX, event.clientY);
    }
}

// Handle keyboard interactions
function handleKeydown(event) {
    if (!isInteractionsBlocked) return;
    
    const interactiveElement = event.target.closest(
        'button, input, select, textarea, a, [role="button"], [tabindex]:not([tabindex="-1"])'
    );
    
    if (interactiveElement) {
        // Allow navigation keys
        const allowedKeys = ['Tab', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Escape'];
        if (!allowedKeys.includes(event.key)) {
            event.preventDefault();
            
            // Add blur effect
            interactiveElement.classList.add('interaction-blocked-blur');
            
            // Remove blur effect after animation
            setTimeout(() => {
                interactiveElement.classList.remove('interaction-blocked-blur');
            }, 500);
            
            // Show tooltip near element
            const rect = interactiveElement.getBoundingClientRect();
            showTooltip(rect.left + rect.width / 2, rect.top + rect.height / 2);
        }
    }
}

// Block all interactions
function blockInteractions() {
    if (isInteractionsBlocked) return;
    isInteractionsBlocked = true;
    
    // Inject blur styles
    injectBlurStyles();
    
    // Create tooltip if needed
    if (!tooltip) createTooltip();
    
    // Add event listeners
    document.addEventListener('mousedown', handleInteraction, true);
    document.addEventListener('click', handleInteraction, true);
    document.addEventListener('touchstart', handleInteraction, true);
    document.addEventListener('keydown', handleKeydown, true);
    document.addEventListener('submit', (e) => e.preventDefault(), true);
    
    // Add visual cue
    document.body.style.cursor = 'not-allowed';
}

// Restore interactions
function restoreInteractions() {
    if (!isInteractionsBlocked) return;
    isInteractionsBlocked = false;
    
    // Remove event listeners
    document.removeEventListener('mousedown', handleInteraction, true);
    document.removeEventListener('click', handleInteraction, true);
    document.removeEventListener('touchstart', handleInteraction, true);
    document.removeEventListener('keydown', handleKeydown, true);
    
    // Remove visual cue
    document.body.style.cursor = '';
    
    // Remove any remaining blur effects
    document.querySelectorAll('.interaction-blocked-blur').forEach(el => {
        el.classList.remove('interaction-blocked-blur');
    });
}

// Usage:
// blockInteractions(); // Disable interactions
// restoreInteractions(); // Enable interactions