{% extends "base-app.html" %}

{% block title %}Leads{% endblock %}

{% block content %}
        <!-- Toolbar -->
        <div class="flex items-center justify-between mt-8">
            <div class="flex items-center gap-4">
                <label class="input input-bordered flex items-center gap-2 h-10 w-64 bg-white">
                    <i class="fa-solid fa-magnifying-glass text-gray-400"></i>
                    <input type="text"
                           name="search"
                           id="search-input"
                           class="grow text-sm"
                           placeholder="Search leads..." />
                </label>
                <div id="stats-container" class="flex items-center gap-4 text-gray-500 text-sm p-2 rounded-lg bg-gray-100">
                    <div class="flex items-center gap-1.5" title="Total Leads">
                        <i class="fa-solid fa-users"></i>
                        <span class="font-semibold">{{ stats.total_leads }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Cold Leads">
                        <i class="fa-solid fa-snowflake"></i>
                        <span class="font-semibold">{{ stats.cold }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Warm Leads">
                        <i class="fa-solid fa-fire"></i>
                        <span class="font-semibold">{{ stats.warm }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="LinkedIn Source">
                        <i class="fa-brands fa-linkedin"></i>
                        <span class="font-semibold">{{ stats.linkedin_source }}</span>
                    </div>
                </div>
            </div>
            <div class="flex items-center gap-3">
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn bg-white border border-base-300 h-10 min-h-10 rounded-lg px-4 text-sm">
                        <i class="fa-solid fa-filter mr-2 text-gray-500"></i>Filters
                    </div>
                    <div tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-white rounded-box w-52 border border-gray-200">
                        <div class="p-2">
                            <label class="label">
                                <span class="label-text font-medium">Lead Type</span>
                            </label>
                            <select name="lead_type"
                                    id="lead-type-filter"
                                    class="select select-bordered select-sm w-full bg-white">
                                <option value="">All Types</option>
                                <option value="cold">Cold</option>
                                <option value="warm">Warm</option>
                            </select>
                        </div>
                        <div class="p-2">
                            <label class="label">
                                <span class="label-text font-medium">Source</span>
                            </label>
                            <select name="source"
                                    id="source-filter"
                                    class="select select-bordered select-sm w-full bg-white">
                                <option value="">All Sources</option>
                                <option value="linkedin">LinkedIn</option>
                                <option value="manual">Manual</option>
                            </select>
                        </div>
                    </div>
                </div>
                <a href="{% url 'leads_lists' %}" class="btn btn-primary h-10 min-h-10 rounded-lg px-4 text-sm">View all lists</a>
            </div>
        </div>

        <!-- Leads Container -->
        <div id="leads-container">
            <!-- Bulk Actions Bar -->
            <div id="bulk-actions" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 mt-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-blue-700">
                        <span id="selected-count">0</span> leads selected
                    </span>
                    <div class="flex items-center gap-2">
                        <button class="btn btn-sm btn-primary"
                                onclick="addToList()"
                                id="add-to-list-btn">
                            <i class="fa-solid fa-plus mr-1"></i>
                            Add to List
                        </button>
                        <button class="btn btn-sm btn-error"
                                onclick="deleteSelected()"
                                id="delete-selected-btn">
                            <i class="fa-solid fa-trash mr-1"></i>
                            Delete Selected
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="clearSelection()">
                            Clear Selection
                        </button>
                    </div>
                </div>
            </div>

            <!-- Table Container -->
            <div class="mt-4 flex-grow overflow-x-auto rounded-lg border border-gray-200 bg-white">
                <table class="table-auto w-full custom-table">
                    <thead>
                        <tr>
                            <th class="w-12 text-center">
                                <input type="checkbox"
                                       class="checkbox checkbox-sm rounded"
                                       id="select-all-checkbox"
                                       onchange="toggleSelectAll()" />
                            </th>
                            <th class="w-16"></th> <!-- Index number column -->
                            <th class="text-left">Lead</th>
                            <th class="text-left">Company</th>
                            <th class="text-left">Position</th>
                            <th class="text-left">Location</th>
                            <th class="text-left">Type</th>
                            <th class="text-left">Source</th>
                            <th class="text-left">Created at</th>
                            <th class="w-12"></th> <!-- Actions column -->
                        </tr>
                    </thead>
                    <tbody id="leads-table-body">
                        {% for lead in leads %}
                        <tr class="hover:bg-gray-50 lead-row">
                            <td class="text-center">
                                <input type="checkbox"
                                       class="checkbox checkbox-sm rounded lead-checkbox"
                                       value="{{ lead.id }}"
                                       onchange="updateBulkActions()" />
                            </td>
                            <td class="text-gray-500 text-center">{{ forloop.counter }}</td>
                            <td class="font-medium text-gray-700">
                                <div class="flex flex-col">
                                    <span>{{ lead.full_name }}</span>
                                    <span class="text-sm text-gray-500">{{ lead.email }}</span>
                                    {% if lead.linkedin_profile %}
                                    <span class="text-xs text-blue-500">
                                        <a href="{{ lead.linkedin_profile }}" target="_blank" class="hover:underline">
                                            <i class="fa-brands fa-linkedin mr-1"></i>LinkedIn
                                        </a>
                                    </span>
                                    {% else %}
                                    <span class="text-xs text-gray-400">📞 {{ lead.phone_number|default:"N/A" }}</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="text-gray-600">
                                <div class="flex flex-col">
                                    <span>{{ lead.company_name }}</span>
                                    {% if lead.company_website %}
                                    <span class="text-sm text-blue-500">
                                        <a href="{{ lead.company_website }}" target="_blank" class="hover:underline">
                                            <i class="fa-solid fa-link mr-1"></i>Website
                                        </a>
                                    </span>
                                    {% else %}
                                    <span class="text-sm text-gray-400">No website</span>
                                    {% endif %}
                                    <span class="text-xs text-gray-400">{{ lead.industry }} • {{ lead.employee_count }} employees</span>
                                </div>
                            </td>
                            <td class="text-gray-600">{{ lead.position }}</td>
                            <td class="text-gray-600">{{ lead.location }}</td>
                            <td class="text-gray-600">
                                {% if lead.lead_type == 'cold' %}
                                    <span class="badge badge-ghost gap-1 font-medium py-2">
                                        <i class="fa-solid fa-snowflake"></i> {{ lead.lead_type }}
                                    </span>
                                {% elif lead.lead_type == 'warm' %}
                                    <span class="badge badge-warning gap-1 font-medium py-2">
                                        <i class="fa-solid fa-fire"></i> {{ lead.lead_type }}
                                    </span>
                                {% elif lead.lead_type == 'hot' %}
                                    <span class="badge badge-error gap-1 font-medium py-2">
                                        <i class="fa-solid fa-fire"></i> {{ lead.lead_type }}
                                    </span>   
                                {% elif lead.lead_type == 'customer' %}
                                    <span class="badge badge-success gap-1 font-medium py-2">
                                        <i class="fa-solid fa-true"></i> {{ lead.lead_type }}
                                    </span>   
                                {% endif %}      
                            </td>
                            <td class="text-gray-600">
                                <span class="badge badge-info gap-1 font-medium py-2">
                                    {% if lead.source == 'linkedin_scrape' %}
                                        <i class="fa-brands fa-linkedin"></i> {{ lead.source }}
                                    {% else %}
                                        <i class="fa-brands fa-google"></i> {{ lead.source }}    
                                    {% endif %}
                                </span>
                            </td>
                            <td class="text-gray-600">{{ lead.created_at }}</td>
                            <td class="text-center">
                                <div class="dropdown dropdown-end">
                                    <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                                        <i class="fa-solid fa-ellipsis-vertical"></i>
                                    </div>
                                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-white rounded-box w-40 border border-gray-200">
                                        <li><a href="#" class="text-blue-600"><i class="fa-solid fa-eye mr-2"></i>View Details</a></li>
                                        <li><a href="#" class="text-green-600"><i class="fa-solid fa-plus mr-2"></i>Add to list</a></li>
                                        <li><a href="#" onclick="deleteLead({{ lead.id }})" class="text-red-600"><i class="fa-solid fa-trash mr-2"></i>Delete</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        {% endfor %}

                        <!-- Empty state (hidden by default, shown when no leads match filters) -->
                        <tr id="no-leads-row" class="hidden">
                            <td colspan="10" class="text-center py-16">
                                <div class="flex flex-col items-center justify-center">
                                    <!-- SVG Illustration -->
                                    <div class="mb-6 opacity-90">
                                        <img src="https://raw.githubusercontent.com/Othunderlight/static/refs/heads/main/no-campaign-lead.svg"
                                             alt="No leads illustration"
                                             class="w-64 h-auto mx-auto">
                                    </div>

                                    <!-- Message -->
                                    <div class="text-center">
                                        <h3 class="text-lg font-semibold text-gray-800 mb-2">
                                            👋 Add some leads to get started
                                        </h3>
                                        <p class="text-gray-500 mb-6">
                                            Upload your leads or add them manually to start your outreach
                                        </p>

                                        <!-- Add Leads Button -->
                                        <button class="btn btn-primary h-10 min-h-10 rounded-lg px-4 text-sm">
                                            Add Lead
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div> <!-- End leads-container -->

        <!-- Footer -->
        <div class="py-6 text-center">
            <hr class="mb-4">
            <div class="flex items-center justify-center gap-4">
                <span class="text-sm text-gray-400" id="search-status">Showing all leads</span>
                <button class="btn btn-sm bg-white border border-gray-300" disabled>No more to load</button>
            </div>
        </div>

    </div>
</div>









<!-- Right Sidebar (Drawer) -->
<div id="sidebar" class="fixed top-0 right-0 h-full w-full sm:w-3/4 md:w-2/3 lg:w-[30%] xl:max-w-4xl bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 flex flex-col">
    
    <!-- Drawer Header -->
    <div class="flex justify-between items-center p-6 border-b border-gray-200">
        <h2 class="text-base font-semibold text-gray-800" id="sidebar-email"><EMAIL></h2>
        <button onclick="closeSidebar()" class="btn btn-ghost btn-circle btn-sm -mr-2" aria-label="Close sidebar">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>

    <!-- Drawer Content -->
    <div class="flex-grow overflow-y-auto">
        <div class="p-6">
            <!-- Tabs -->
            <div role="tablist" class="tabs tabs-bordered">
                <a role="tab" class="tab h-12 tab-active font-medium text-sm" onclick="switchTab('settings')">Settings</a>
                <a role="tab" class="tab h-12 text-gray-500 font-medium text-sm" onclick="switchTab('campaigns')">Campaigns</a>
            </div>

            <!-- Settings Tab Content -->
            <div id="settings-tab" class="mt-6 space-y-6">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">First Name</span>
                        </label>
                        <input type="text" id="first-name" class="input input-bordered w-full bg-white" placeholder="First Name">
                    </div>
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Last Name</span>
                        </label>
                        <input type="text" id="last-name" class="input input-bordered w-full bg-white" placeholder="Last Name">
                    </div>
                </div>
                <div>
                    <label class="label">
                        <span class="label-text font-medium">Signature</span>
                    </label>
                    <textarea id="signature" class="textarea textarea-bordered w-full bg-white h-24" placeholder="Your email signature"></textarea>
                </div>
                <div>
                    <label class="label">
                        <span class="label-text font-medium">Daily campaign limit</span>
                    </label>
                    <div class="flex items-center">
                        <input type="number" id="daily-campaign-limit" class="input input-bordered bg-white" value="30">
                        <span class="ml-4">emails</span>
                    </div>
                </div>
                <div>
                    <label class="label">
                        <span class="label-text font-medium">Daily sending limit</span>
                    </label>
                    <div class="flex items-center">
                        <input type="number" id="daily-sending-limit" class="input input-bordered bg-white" value="30">
                        <span class="ml-4">emails</span>
                    </div>
                </div>
                <div>
                    <label class="label">
                        <span class="label-text font-medium">Minimum wait time</span>
                    </label>
                    <div class="flex items-center">
                        <input type="number" id="min-wait-time" class="input input-bordered bg-white" value="1">
                        <span class="ml-4">minute(s)</span>
                    </div>
                    <label class="label">
                        <span class="label-text-alt">When used with multiple campaigns</span>
                    </label>
                </div>
                <button class="btn btn-primary w-full" onclick="saveSettings()">Save Settings</button>
            </div>

            <!-- Campaigns Tab Content -->
            <div id="campaigns-tab" class="mt-6 hidden">
                <p class="text-gray-600">Campaigns content goes here.</p>
            </div>
        </div>
    </div>
</div>

<!-- Overlay (hidden by default) -->
<div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>












<script>
    // right side bar
function openSidebar(email) {
    document.getElementById('sidebar').classList.remove('translate-x-full');
    document.getElementById('overlay').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
    document.getElementById('sidebar-email').textContent = email;
}

function closeSidebar() {
    document.getElementById('sidebar').classList.add('translate-x-full');
    document.getElementById('overlay').classList.add('hidden');
    document.body.style.overflow = 'auto';
}



// Bulk selection functionality
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const leadCheckboxes = document.querySelectorAll('.lead-checkbox');

    leadCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBulkActions();
}

function updateBulkActions() {
    const leadCheckboxes = document.querySelectorAll('.lead-checkbox');
    const checkedBoxes = document.querySelectorAll('.lead-checkbox:checked');
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');

    // Update select all checkbox state
    if (checkedBoxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedBoxes.length === leadCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }

    // Show/hide bulk actions
    if (checkedBoxes.length > 0) {
        bulkActions.classList.remove('hidden');
        selectedCount.textContent = checkedBoxes.length;
    } else {
        bulkActions.classList.add('hidden');
    }
}

function clearSelection() {
    const leadCheckboxes = document.querySelectorAll('.lead-checkbox');
    const selectAllCheckbox = document.getElementById('select-all-checkbox');

    leadCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = false;

    updateBulkActions();
}

function deleteSelected() {
    const checkedBoxes = document.querySelectorAll('.lead-checkbox:checked');
    const leadIds = Array.from(checkedBoxes).map(cb => cb.value);

    if (leadIds.length === 0) return;

    // Use the modal system
    ModalSystem.confirm({
        title: 'Delete Selected Leads',
        message: `Are you sure you want to delete ${leadIds.length} selected lead(s)? This action cannot be undone.`,
        confirmText: 'Delete',
        confirmClass: 'btn-error',
        action: function() {
            // Simulate deletion by removing rows
            checkedBoxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                row.remove();
            });

            // Clear selection
            clearSelection();

            // Show success toast
            ModalSystem.toast(`${leadIds.length} lead(s) deleted successfully!`);

            // Trigger HTMX refresh to update stats
            htmx.trigger('#search-input', 'keyup');
        }
    });
}

function deleteLead(leadId) {
    // Use the modal system
    ModalSystem.confirm({
        title: 'Delete Lead',
        message: 'Are you sure you want to delete this lead? This action cannot be undone.',
        confirmText: 'Delete',
        confirmClass: 'btn-error',
        action: function() {
            // Find and remove the row
            const checkbox = document.querySelector(`input[value="${leadId}"]`);
            if (checkbox) {
                const row = checkbox.closest('tr');
                row.remove();
            }

            // Update bulk actions
            updateBulkActions();

            // Show success toast
            ModalSystem.toast('Lead deleted successfully!');

            // Trigger HTMX refresh to update stats
            htmx.trigger('#search-input', 'keyup');
        }
    });
}

function addToList() {
    const checkedBoxes = document.querySelectorAll('.lead-checkbox:checked');
    const leadIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (leadIds.length === 0) return;
    
    // Show modal to select list
    ModalSystem.confirm({
        title: 'Add to List',
        message: `Select a list to add ${leadIds.length} lead(s) to:`,
        confirmText: 'Add',
        confirmClass: 'btn-primary',
        customContent: `
            <select id="list-select" class="select select-bordered w-full mt-2">
                <option value="">Select a list</option>
                <option value="1">Summer Outreach 2025</option>
                <option value="2">Tech Startups Q3</option>
                <option value="3">MENA Region Prospects</option>
            </select>
        `,
        action: function() {
            const listId = document.getElementById('list-select').value;
            if (!listId) {
                ModalSystem.toast('Please select a list', 'error');
                return;
            }
            
            // Show success toast
            ModalSystem.toast(`${leadIds.length} lead(s) added to list successfully!`);
            
            // Clear selection
            clearSelection();
        }
    });
}

function clearFilters() {
    document.getElementById('search-input').value = '';
    document.getElementById('lead-type-filter').value = '';
    document.getElementById('source-filter').value = '';
    filterLeads();
}

// Filter functionality
function filterLeads() {
    const searchTerm = document.getElementById('search-input').value.toLowerCase();
    const leadTypeFilter = document.getElementById('lead-type-filter').value.toLowerCase();
    const sourceFilter = document.getElementById('source-filter').value.toLowerCase();

    const rows = document.querySelectorAll('.lead-row');
    let visibleCount = 0;

    rows.forEach(row => {
        const leadText = row.textContent.toLowerCase();

        // Get lead type from badge
        const typeBadge = row.querySelector('.badge');
        const leadType = typeBadge ? typeBadge.textContent.toLowerCase() : '';

        // Get source from second badge
        const badges = row.querySelectorAll('.badge');
        const source = badges.length > 1 ? badges[1].textContent.toLowerCase() : '';

        const matchesSearch = searchTerm === '' || leadText.includes(searchTerm);
        const matchesType = leadTypeFilter === '' || leadType.includes(leadTypeFilter);
        const matchesSource = sourceFilter === '' || source.includes(sourceFilter);

        if (matchesSearch && matchesType && matchesSource) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Show/hide empty state
    const noLeadsRow = document.getElementById('no-leads-row');
    if (visibleCount === 0) {
        noLeadsRow.classList.remove('hidden');
    } else {
        noLeadsRow.classList.add('hidden');
    }

    // Update search status
    const searchStatus = document.getElementById('search-status');
    if (searchStatus) {
        if (searchTerm || leadTypeFilter || sourceFilter) {
            searchStatus.textContent = `Showing ${visibleCount} of {{ stats.total_leads }} leads`;
        } else {
            searchStatus.textContent = 'Showing all leads';
        }
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateBulkActions();

    // Add event listeners for filters
    document.getElementById('search-input').addEventListener('input', filterLeads);
    document.getElementById('lead-type-filter').addEventListener('change', filterLeads);
    document.getElementById('source-filter').addEventListener('change', filterLeads);
});
</script>
{% endblock %}
