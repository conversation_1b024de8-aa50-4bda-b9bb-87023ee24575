{% extends "app/campaign/campaign-base.html" %}
{% load static %}
{% block title %}Campaign Leads{% endblock %}

{% block cmp-base-content %}
<style>
    /* lead-lists Grid Styles */
    .lead-lists-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 2rem;
        padding: 2rem;
    }
    
    .lead-lists-card {
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1.25rem;
        transition: all 0.2s;
        cursor: pointer;
        background-color: white;
    }
    
    .lead-lists-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .stats-bar {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
        font-size: 0.875rem;
    }

    /* Table Styles */
    .table-zebra tbody tr:nth-child(even) {
        background-color: #f9fafb;
    }

    /* Stats Cards */
    .stats {
        border-radius: 0.5rem;
        padding: 1rem;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
    }
</style>

<div id="first" class="flex flex-col h-full">
    <!-- Header -->
    <div class="flex items-center justify-between mt-8">
        <div class="flex items-center gap-4">
            <h3 class="text-xl font-semibold">Campaign Leads Lists</h3>
        </div>
        <div class="flex items-center gap-3">  
            <button class="btn btn-primary h-10 min-h-10 rounded-lg px-4 text-sm" onclick="document.getElementById('add-modal').showModal()">
                Assign Lead List
            </button>
        </div>
    </div>

    <!-- lead-lists Grid -->
    <div class="lead-lists-grid">
        {% for list in lead_lists %}
        <a  href="{% url 'leads_view' list.id  %}" class="lead-lists-card" style="padding-bottom: 10px; padding-right: 15px; min-width: 330px;"> 
            <h3 class="font-medium text-gray-900">{{list.title}}</h3>
            
            <div class="stats-bar mb-4">
                <span class="text-blue-600">
                    {{list.count}} Leads
                </span>
            </div>
            <div class="flex flex-wrap gap-1 mb-6">
            {% for tag in list.tags.split %}
                <span class="badge badge-gray text-gray-600">{{ tag }}</span>
            {% endfor %}
            </div>
            <div class="card-actions justify-end">
            <button class="btn btn-sm btn-ghost text-error pr-0 pl-0" onclick="event.preventDefault(); event.stopPropagation(); unAssignListFromCampaign({{ list.id }})" title="unassign list from this campaign">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                unassign
            </button>
        </div>
        </a>
        {% empty %}
        <div class="col-span-full text-center py-12">
            <div class="text-gray-400 mb-4">
                <i class="fas fa-envelope-open-text fa-3x"></i>
            </div>
            <h3 class="text-lg font-medium">No lead-lists assigned yet</h3>
            <button class="btn btn-primary mt-4" onclick="document.getElementById('add-modal').showModal()">
                Assign Lead List
            </button>
        </div>
        {% endfor %}
    </div>
</div>

<!-- assign lead-lists Modal -->
<dialog id="add-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg">assign lead-lists to the campaign</h3>
        
        <div class="py-4">
            <label class="label">
                <span class="label-text">Select lead-lists</span>
            </label>
            <select class="select select-bordered w-full" id="lead-list-select">
                {% for list in lead_lists_assign %}
                    <option value="{{ list.id }}">{{ list.title }}</option>
                    {% empty %}
                    <option value="">No lead-lists available</option>
                {% endfor %}
            </select>
        </div>
        
        <div class="modal-action">
            <button class="btn btn-primary" onclick="assignList2Campaign()">Assign</button>
            <form method="dialog">
                <button class="btn btn-ghost">Cancel</button>
            </form>
        </div>
    </div>
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>


<script src="{% static 'js/block-interactions.js' %}"></script> 
<script>
    
    {% if view_only %}
        blockInteractions('#first');
    {% endif %}

function assignList2Campaign() {
    const selectElement = document.getElementById("lead-list-select");
    const listId = selectElement.value;
    const campaignId = {{ campaign_id }};

    fetch(`/api/leads/lists/${listId}/assign/`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
            "X-CSRFToken": "{{ csrf_token }}",
        },
        body: JSON.stringify({
            campaign_id: campaignId,
        }),
    })
    .then(response => {
        if (response.ok) {
            ModalSystem.toast('Lead List assigned successfully to this Campaign.', 'success');
            ModalSystem.close('add-modal')
            window.location.reload()
        } else {
            ModalSystem.toast('Failed to assign Lead List to this Campaign.', 'error');
        }
    })
    .catch(error => {
        console.error("Error:", error);
        ModalSystem.toast('An error occurred.', 'error');
    });
}


function unAssignListFromCampaign(listId) {
    const campaignId = {{ campaign_id }};

    fetch(`/api/leads/lists/${listId}/unassign/`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
            "X-CSRFToken": "{{ csrf_token }}",
        },
        body: JSON.stringify({
            campaign_id: campaignId,
        }),
    })
    .then(response => {
        if (response.ok) {
            ModalSystem.toast("Lead list unassigned from this Campaign", "success")
            window.location.reload()
        } else {
            ModalSystem.toast('Failed to assign list to campaign.', 'error');
        }
    })
    .catch(error => {
        console.error("Error:", error);
        ModalSystem.toast('An error occurred.', 'error');
    });
}


</script>

{% endblock %}


}