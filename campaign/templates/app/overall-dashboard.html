{% extends "base-app.html" %}
{% load static %}

{% block title %}Email Marketing Dashboard{% endblock %}


{% block js %}
<script src="{% static 'cdn/chart.js' %}"></script>
{% endblock %}


{% block content %}
<div class="p-6 md:p-8">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between mb-6 gap-4">
        <div>
            <h1 class="text-3xl font-bold text-base-content">Email Analytics</h1>
            <p class="text-base-content/70">Monitor your email campaign performance</p>
        </div>
        <div class="flex items-center gap-4 flex-wrap">
            <div class="dropdown dropdown-end">
                <div tabindex="0" role="button" class="btn btn-ghost">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.186 2.25 2.25 0 0 0-3.933 2.186Z" />
                    </svg>
                    Share
                </div>
                <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                    <li><a>Export as PDF</a></li>
                    <li><a>Share via email</a></li>
                    <li><a>Generate report</a></li>
                </ul>
            </div>
            <div class="dropdown dropdown-end">
                <div tabindex="0" role="button" class="btn" id="time-filter-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                    </svg>
                    <span id="time-filter-text">Last 30 days</span>
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                </div>
                <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                    <li><a onclick="changeTimeFilter('7d', 'Last 7 days')">Last 7 days</a></li>
                    <li><a onclick="changeTimeFilter('30d', 'Last 30 days')" class="active">Last 30 days</a></li>
                    <li><a onclick="changeTimeFilter('90d', 'Last 90 days')">Last 90 days</a></li>
                    <li><a onclick="changeTimeFilter('all', 'All time')">All time</a></li>
                </ul>
            </div>
            <button class="btn btn-primary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                New Campaign
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
        <div class="card bg-gradient-to-br from-primary/10 to-primary/5 shadow-sm hover:shadow-md transition-shadow">
            <div class="card-body p-4">
                <div class="flex items-center justify-between">
                    <h2 class="text-sm font-medium text-base-content/70">Sequence Started</h2>
                    <div class="badge badge-primary badge-sm" id="sequence-badge">-</div>
                </div>
                <p class="text-3xl font-bold mt-1" id="sequence-started-count">-</p>
                <div class="text-xs text-primary mt-2">
                    <span class="font-medium" id="sequence-started-rate">-</span> of total leads
                </div>
            </div>
        </div>

        <div class="card bg-gradient-to-br from-info/10 to-info/5 shadow-sm hover:shadow-md transition-shadow">
            <div class="card-body p-4">
                <div class="flex items-center justify-between">
                    <h2 class="text-sm font-medium text-base-content/70">Open Rate</h2>
                    <div class="badge badge-info badge-sm" id="open-badge">-</div>
                </div>
                <p class="text-3xl font-bold mt-1" id="open-rate">-</p>
                <div class="text-xs text-info mt-2">
                    <span class="font-medium" id="opened-count">-</span> total opens
                </div>
            </div>
        </div>

        <div class="card bg-gradient-to-br from-secondary/10 to-secondary/5 shadow-sm hover:shadow-md transition-shadow">
            <div class="card-body p-4">
                <div class="flex items-center justify-between">
                    <h2 class="text-sm font-medium text-base-content/70">Click Rate</h2>
                    <div class="badge badge-secondary badge-sm" id="click-badge">-</div>
                </div>
                <p class="text-3xl font-bold mt-1" id="click-rate">-</p>
                <div class="text-xs text-secondary mt-2">
                    <span class="font-medium" id="clicked-count">-</span> total clicks
                </div>
            </div>
        </div>

        <div class="card bg-gradient-to-br from-accent/10 to-accent/5 shadow-sm hover:shadow-md transition-shadow">
            <div class="card-body p-4">
                <div class="flex items-center justify-between">
                    <h2 class="text-sm font-medium text-base-content/70">Reply Rate</h2>
                    <div class="badge badge-accent badge-sm" id="reply-badge">-</div>
                </div>
                <p class="text-3xl font-bold mt-1" id="reply-rate">-</p>
                <div class="text-xs text-accent mt-2">
                    <span class="font-medium" id="replied-count">-</span> total replies
                </div>
            </div>
        </div>

        <div class="card bg-gradient-to-br from-success/10 to-success/5 shadow-sm hover:shadow-md transition-shadow">
            <div class="card-body p-4">
                <div class="flex items-center justify-between">
                    <h2 class="text-sm font-medium text-base-content/70">Revenue</h2>
                    <div class="badge badge-success badge-sm" id="revenue-badge">-</div>
                </div>
                <p class="text-3xl font-bold mt-1" id="revenue-total">-</p>
                <div class="text-xs text-success mt-2">
                    <span class="font-medium" id="opportunities-count">-</span> opportunities
                </div>
            </div>
        </div>
    </div>

  <!-- Main Analytics Chart -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <!-- Custom Legend -->
            <div class="flex flex-wrap gap-x-4 gap-y-2 mb-4">
                <div class="flex items-center gap-2 text-sm"><div class="w-3 h-3 rounded-full bg-blue-500"></div>Sent</div>
                <div class="flex items-center gap-2 text-sm"><div class="w-3 h-3 rounded-full bg-sky-400"></div>Total opens</div>
                <div class="flex items-center gap-2 text-sm"><div class="w-3 h-3 rounded-full bg-teal-400"></div>Unique opens</div>
                <div class="flex items-center gap-2 text-sm"><div class="w-3 h-3 rounded-full bg-purple-400"></div>Total replies</div>
                <div class="flex items-center gap-2 text-sm"><div class="w-3 h-3 rounded-full bg-amber-400"></div>Total clicks</div>
            </div>
            <div class="chart-container" style="height: 400px;">
                <canvas id="mainAnalyticsChart"></canvas>
            </div>
        </div>
    </div>


    <!-- Campaign Analytics Table -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body p-0">
            <div class="flex flex-wrap justify-between items-center p-6 pb-0">
                <h2 class="text-xl font-semibold">Recent Campaigns</h2>
                <div class="tabs tabs-boxed">
                    <a class="tab tab-active">All</a> 
                    <a class="tab">Active</a> 
                    <a class="tab">Drafts</a>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Campaign</th>
                            <th>Status</th>
                            <th>Sent</th>
                            <th>Opens</th>
                            <th>Clicks</th>
                            <th>Revenue</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="campaigns-table-body">
                        <!-- Dynamic campaign rows will be inserted here -->
                        <tr>
                            <td colspan="7" class="text-center py-8">
                                <div class="loading loading-spinner loading-md"></div>
                                <p class="mt-2 text-base-content/60">Loading campaigns...</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="p-4 flex justify-center">
                <button class="btn btn-ghost">View All Campaigns</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        let mainAnalyticsChart;
        let currentTimeFilter = '30d';

        // API functions
        const fetchOverallAnalyticsData = async (timeFilter = '30d') => {
            try {
                const response = await fetch(`/campaign/api/overall-analytics/?filter=${timeFilter}`);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Error fetching overall analytics data:', error);
                return null;
            }
        };

        // Update summary cards with new data
        const updateSummaryCards = (summaryStats) => {
            document.getElementById('sequence-started-count').textContent = summaryStats.sequence_started_count.toLocaleString();
            document.getElementById('sequence-started-rate').textContent = `${summaryStats.sequence_started_rate}%`;
            document.getElementById('sequence-badge').textContent = `${summaryStats.sequence_started_rate}%`;

            document.getElementById('open-rate').textContent = `${summaryStats.open_rate_percentage}%`;
            document.getElementById('opened-count').textContent = summaryStats.opened_count.toLocaleString();
            document.getElementById('open-badge').textContent = `${summaryStats.open_rate_percentage}%`;

            document.getElementById('click-rate').textContent = `${summaryStats.click_rate_percentage}%`;
            document.getElementById('clicked-count').textContent = summaryStats.clicked_count.toLocaleString();
            document.getElementById('click-badge').textContent = `${summaryStats.click_rate_percentage}%`;

            document.getElementById('reply-rate').textContent = `${summaryStats.reply_rate_percentage}%`;
            document.getElementById('replied-count').textContent = summaryStats.replied_count.toLocaleString();
            document.getElementById('reply-badge').textContent = `${summaryStats.reply_rate_percentage}%`;

            document.getElementById('revenue-total').textContent = `$${summaryStats.conversions_total_value.toLocaleString()}`;
            document.getElementById('opportunities-count').textContent = summaryStats.opportunities_count.toLocaleString();
            document.getElementById('revenue-badge').textContent = `$${summaryStats.opportunities_total_value.toLocaleString()}`;
        };

        const chartOptions = (theme) => {
            const isDark = theme === 'dark';
            return {
                maintainAspectRatio: false,
                responsive: true,
                plugins: {
                    legend: {
                        display: false // Using custom HTML legend
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: { color: isDark ? '#A6ADBB' : '#4B5563' },
                        grid: { color: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)' }
                    },
                    x: {
                        ticks: { color: isDark ? '#A6ADBB' : '#4B5563' },
                        grid: { display: false }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            };
        };

        // Update campaigns table
        const updateCampaignsTable = (campaigns) => {
            const tbody = document.getElementById('campaigns-table-body');

            if (campaigns.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center py-8">
                            <p class="text-base-content/60">No campaigns found</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = campaigns.map(campaign => {
                const statusBadge = campaign.status === 'active' ? 'badge-success' :
                                  campaign.status === 'draft' ? 'badge-warning' : 'badge-info';
                const statusText = campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1);

                return `
                    <tr>
                        <td>
                            <div class="flex items-center gap-3">
                                <div class="avatar">
                                    <div class="w-10 rounded bg-primary/10 text-primary flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                </div>
                                <div>
                                    <div class="font-bold">${campaign.name}</div>
                                    <div class="text-sm opacity-50">${campaign.created_at}</div>
                                </div>
                            </div>
                        </td>
                        <td><span class="badge ${statusBadge} badge-sm">${statusText}</span></td>
                        <td>${campaign.sequence_started_count.toLocaleString()}</td>
                        <td>
                            <div class="flex items-center gap-2">
                                ${campaign.opened_count.toLocaleString()}
                                <span class="text-success text-sm">(${campaign.open_rate_percentage}%)</span>
                            </div>
                        </td>
                        <td>
                            <div class="flex items-center gap-2">
                                ${campaign.clicked_count.toLocaleString()}
                                <span class="text-success text-sm">(${campaign.click_rate_percentage}%)</span>
                            </div>
                        </td>
                        <td class="text-success font-medium">$${campaign.conversions_total_value.toLocaleString()}</td>
                        <td>
                            <a href="/campaign/dashboard/${campaign.id}/" class="btn btn-ghost btn-xs">View</a>
                        </td>
                    </tr>
                `;
            }).join('');
        };

        const createOrUpdateChart = (chartData) => {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const ctx = document.getElementById('mainAnalyticsChart');
            if (!ctx) return;

            if (mainAnalyticsChart) {
                mainAnalyticsChart.data = chartData;
                mainAnalyticsChart.options = chartOptions(currentTheme);
                mainAnalyticsChart.update();
            } else {
                mainAnalyticsChart = new Chart(ctx.getContext('2d'), {
                    type: 'bar',
                    data: chartData,
                    options: chartOptions(currentTheme)
                });
            }
        };

        // Load and display analytics data
        const loadAnalyticsData = async (timeFilter = '30d') => {
            const data = await fetchOverallAnalyticsData(timeFilter);
            if (data && data.success) {
                updateSummaryCards(data.summary_stats);
                createOrUpdateChart(data.chart_data);
                updateCampaignsTable(data.campaigns);
            }
        };

        // Time filter change handler
        window.changeTimeFilter = (filter, text) => {
            currentTimeFilter = filter;
            document.getElementById('time-filter-text').textContent = text;

            // Update active state
            document.querySelectorAll('.dropdown-content a').forEach(a => a.classList.remove('active'));
            event.target.classList.add('active');

            // Reload data
            loadAnalyticsData(filter);
        };

        // Initial data load
        loadAnalyticsData(currentTimeFilter);

        // Optional: If you have a theme switcher in your base template,
        // this will listen for changes and update the chart.
        const observer = new MutationObserver((mutationsList) => {
            for (const mutation of mutationsList) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                    if (mainAnalyticsChart) {
                        mainAnalyticsChart.options = chartOptions(mutation.target.getAttribute('data-theme'));
                        mainAnalyticsChart.update();
                    }
                }
            }
        });
        observer.observe(document.documentElement, { attributes: true });
    });

</script>
{% endblock %}