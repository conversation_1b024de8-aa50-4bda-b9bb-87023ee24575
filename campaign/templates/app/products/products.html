{% extends "base-app.html" %}

{% block title %}Products{% endblock %}

{% block content %}

<div class="flex-1 flex flex-col overflow-hidden bg-gray-50 p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Products</h1>
                <p class="text-gray-600">Manage your products and services for campaign targeting</p>
            </div>
            <div class="flex gap-2">
                <button class="btn btn-primary" onclick="addNewProduct()">
                    <i class="fa-solid fa-plus mr-2"></i>
                    New Product
                </button>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="mb-6">
        <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div class="flex flex-col sm:flex-row gap-4 flex-1">
                <div class="relative flex-1 max-w-md">
                    <input type="text"
                           placeholder="Search products..."
                           class="input input-bordered w-full bg-white pl-10"
                           id="search-products">
                    <i class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <button onclick="clearProductFilters()" class="btn btn-ghost">Clear</button>
            </div>
            <div class="text-sm text-gray-500">
                <span id="product-count">{{ products|length }}</span> products
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="table w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="text-left font-semibold text-gray-900">Product Name</th>
                        <th class="text-left font-semibold text-gray-900">Description</th>
                        <th class="text-left font-semibold text-gray-900">Landing Page URL</th>
                        <th class="text-left font-semibold text-gray-900">Campaigns</th>
                        <th class="text-left font-semibold text-gray-900">Actions</th>
                    </tr>
                </thead>
                <tbody id="products-table-body">
                    {% for product in products %}
                    <tr id= "{{ product.id }}" class="hover:bg-gray-100 cursor-pointer"
                        onclick="openSidebar('{{ product.id }}')"
                        data-name="{{ product.name|lower }}"
                        data-tagline="{{ product.tagline|lower }}"
                        data-description="{{ product.description|lower }}"
                        data-status="{{ product.status|lower }}">
                        <td>
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-desktop text-blue-600"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">{{ product.name }}</div>
                                    <div class="text-sm text-gray-500">{{ product.tagline }}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="max-w-xs">
                                <p class="text-sm text-gray-900 line-clamp-2">
                                    {{ product.description }}
                                </p>
                            </div>
                        </td>
                        <td>
                            <a href="{{ product.landing_page_url }}" target="_blank"
                               class="text-blue-600 hover:text-blue-800 text-sm underline"
                               onclick="event.stopPropagation()">
                                {{ product.landing_page_url|truncatechars:30 }}
                            </a>
                        </td>
                        <td>
                            <span class="text-sm text-gray-500">{{ product.product_campaigns.count }}</span>
                        </td>
                        <td>
                            <div class="flex items-center gap-2">
                                <button class="btn btn-ghost btn-xs" onclick="event.stopPropagation(); editProduct({{ product.id }})" title="Duplicate">
                                   <i class="fa-solid fa-edit"></i>
                                </button>
                                <button class="btn btn-ghost btn-xs" onclick="event.stopPropagation(); viewProduct({{ product.id }})" title="View">
                                    <i class="fa-regular fa-eye"></i>
                                </button>
                                <button class="btn btn-ghost btn-xs text-red-500" onclick="event.stopPropagation(); confirmDeleteProduct({{ product.id }})" title="Delete">
                                    <i class="fa-solid fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr id="no-products-row">
                        <td colspan="5" class="text-center py-16">
                            <div class="flex flex-col items-center justify-center">
                                <img src="https://raw.githubusercontent.com/Othunderlight/static/refs/heads/main/no-products.svg"
                                     alt="No products illustration"
                                     class="w-64 h-auto mx-auto">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2 mt-4">
                                    👋 Create your first product
                                </h3>
                                <p class="text-gray-500 mb-6">
                                    Get started by adding a product for your campaigns
                                </p>
                                <button class="btn btn-primary" onclick="addNewProduct()">
                                    <i class="fa-solid fa-plus mr-2"></i>
                                    New Product
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>






<!-- Right Sidebar (Drawer) -->
<div id="sidebar" class="fixed top-0 right-0 h-full w-full sm:w-3/4 md:w-2/3 lg:w-[30%] xl:max-w-4xl bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 flex flex-col">
    
    <!-- Drawer Header -->
    <div class="flex justify-between items-center p-6 border-b border-gray-200">
        <h2 class="text-base font-semibold text-gray-800" id="sidebar-product">Loading...</h2>
        <div class="flex gap-2">
            <button id="toggle-edit-btn" class="btn btn-sm btn-outline" onclick="toggleEditMode()">Edit</button>
            <button onclick="closeSidebar()" class="btn btn-ghost btn-circle btn-sm" aria-label="Close sidebar">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
    </div>

    <!-- Drawer Content -->
    <div class="flex-grow overflow-y-auto">
        <div class="p-6">
            <div id="settings-tab" class="mt-6 space-y-6">
                <!-- Product Name -->
                <div>
                    <label class="label"><span class="label-text font-medium">Product Name</span></label>
                    <input type="text" id="product-name" class="input input-bordered w-full bg-white hidden">
                    <p id="product-name-view" class="text-gray-800"></p>
                </div>

                <!-- Description -->
                <div>
                    <label class="label"><span class="label-text font-medium">Description</span></label>
                    <textarea id="Description" class="textarea textarea-bordered w-full bg-white h-24 hidden"></textarea>
                    <p id="Description-view" class="text-gray-800 whitespace-pre-line"></p>
                </div>

                <!-- Product URL -->
                <div>
                    <label class="label"><span class="label-text font-medium">Product URL</span></label>
                    <input type="text" id="product-url" class="input input-bordered w-full bg-white hidden">
                    <div id="product-url-view" class="flex items-center gap-2">
                    <a href="#" id="product-url-link" target="_blank" class="text-blue-600 underline break-all">Visit</a>
                    <button onclick="copyToClipboard()" class="btn btn-xs btn-ghost tooltip" data-tip="Copy URL">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16h8M8 12h8m-8-4h8M4 6h16v12H4z" />
                        </svg>
                    </button>
                    </div>
                </div>

                <!-- Save -->
                <button id="save-settings-btn" class="btn btn-primary w-full hidden" onclick="saveSettings()">Save Settings</button>
                </div>

        </div>
    </div>
</div>

<!-- Overlay (hidden by default) -->
<div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>












<!-- Add Product Modal -->
<dialog id="add-product-modal" class="modal">
    <div class="modal-box max-w-xl">
        <div class="flex items-center gap-2 mb-4">
            <button class="btn btn-ghost btn-sm" onclick="ModalSystem.close('add-product-modal')">
                <i class="fa-solid fa-arrow-left"></i>
                Cancel
            </button>
        </div>

        <div class="text-center mb-6">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fa-solid fa-box-open text-blue-600 text-lg"></i>
            </div>
            <h3 class="font-bold text-lg">Add New Product</h3>
            <p class="text-sm text-gray-500">Create a new product for your campaigns</p>
        </div>

        <form id="product-form" class="space-y-4">
            <div>
                <label class="label">
                    <span class="label-text">Product Name *</span>
                </label>
                <input type="text" 
                       placeholder="e.g. CRM Pro, Marketing Suite" 
                       class="input input-bordered w-full bg-white" 
                       id="product-name-add" 
                       required>
            </div>

            <div>
                <label class="label">
                    <span class="label-text">Description *</span>
                </label>
                <textarea class="textarea textarea-bordered w-full bg-white h-32" 
                          id="product-description-add" 
                          placeholder="Detailed description of the product..." 
                          required></textarea>
            </div>

            <div>
                <label class="label">
                    <span class="label-text">Landing Page URL *</span>
                </label>
                <input type="url" 
                       placeholder="https://yourdomain.com/product" 
                       class="input input-bordered w-full bg-white" 
                       id="product-url-add" 
                       required>
            </div>

        </form>

        <div class="modal-action">
            <button class="btn btn-success w-full text-gray-100" onclick="submitProductForm({{request.user.subscribed_company.id}})">
                <i class="fa-solid fa-save mr-2"></i>
                Save Product
            </button>
        </div>
    </div>
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>

<!-- Loading Modal -->
<dialog id="product-loading-modal" class="modal">
    <div class="modal-box max-w-sm text-center">
        <div class="mb-6">
            <div class="loading loading-spinner loading-lg text-primary mx-auto"></div>
        </div>
        <h3 class="font-bold text-lg mb-2">Saving Product</h3>
        <p class="text-gray-600">Please wait while we save your product...</p>
    </div>
</dialog>

<!-- Success Modal -->
<dialog id="product-success-modal" class="modal">
    <div class="modal-box max-w-sm text-center">
        <div class="mb-6">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <i class="fa-solid fa-check text-green-600 text-2xl"></i>
            </div>
        </div>
        <h3 class="font-bold text-lg mb-2">Product Saved!</h3>
        <p class="text-gray-600 mb-6">Your product has been created successfully.</p>

        <div class="modal-action">
            <button class="btn btn-primary w-full" onclick="closeProductSuccessModal()">
                Done
            </button>
        </div>
    </div>
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>


<script>
// side bar logic    
let isEditMode = false;
let originalProductData = {};
let currentProductId = null;

function openSidebar(productId) {
    fetch(`/api/products/${productId}/`)
        .then(res => res.json())
        .then(data => {
            document.getElementById('sidebar').classList.remove('translate-x-full');
            document.getElementById('overlay').classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // Fill both view and input fields
            document.getElementById('sidebar-product').textContent = data.name;

            document.getElementById('product-name').value = data.name;
            document.getElementById('product-name-view').textContent = data.name;

            document.getElementById('Description').value = data.description;
            document.getElementById('Description-view').textContent = data.description;

            document.getElementById('product-url').value = data.landing_page_url;
            document.getElementById('product-url-link').textContent = data.landing_page_url;
            document.getElementById('product-url-link').href = data.landing_page_url;

            originalProductData = data;
            currentProductId = productId;
            setEditMode(false);
        });
}




function closeSidebar() {
    document.getElementById('sidebar').classList.add('translate-x-full');
    document.getElementById('overlay').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

document.getElementById('overlay').addEventListener('click', closeSidebar);

function toggleEditMode() {
    isEditMode = !isEditMode;
    setEditMode(isEditMode);
}

function setEditMode(enable) {
    const toggle = (id, show) => {
        document.getElementById(id).classList.toggle('hidden', !show);
    };

    toggle('product-name', enable);
    toggle('product-name-view', !enable);

    toggle('Description', enable);
    toggle('Description-view', !enable);

    toggle('product-url', enable);
    toggle('product-url-view', !enable);

    document.getElementById('toggle-edit-btn').textContent = enable ? 'Cancel' : 'Edit';
    document.getElementById('save-settings-btn').classList.toggle('hidden', !enable);
}

function saveSettings() {
    const name = document.getElementById('product-name').value;
    const description = document.getElementById('Description').value;
    const url = document.getElementById('product-url').value;

    const payload = {};
    if (name !== originalProductData.name) payload.name = name;
    if (description !== originalProductData.description) payload.description = description;
    if (url !== originalProductData.landing_page_url) payload.landing_page_url = url;

    const method = Object.keys(payload).length > 1 ? 'PUT' : 'PATCH';
    const fullPayload = method === 'PUT' ? {
        subscribed_company: originalProductData.subscribed_company,
        name: name,
        description: description,
        landing_page_url: url
    } : payload;

    fetch(`/api/products/${currentProductId}/`, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(fullPayload)
    })
    .then(res => {
        if (!res.ok) throw new Error("Failed to save");
        return res.json();
    })
    .then(data => {
        alert("Saved!");
        openSidebar(currentProductId);
    })
    .catch(err => {
        alert("Save failed");
        console.error(err);
    });
}





// Product Modal Functions
function addNewProduct() {
    // Reset form if needed
    document.getElementById('product-form').reset();
    ModalSystem.open('add-product-modal');
}

function submitProductForm(CompanyID) {
    const form = document.getElementById('product-form');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Show loading modal
    ModalSystem.close('add-product-modal');
    ModalSystem.open('product-loading-modal');

    // Collect form data
    const productData = {
        name: document.getElementById('product-name-add').value,
        description: document.getElementById('product-description-add').value,
        landing_page_url: document.getElementById('product-url-add').value,
        subscribed_company: CompanyID,
    };

    fetch(`/api/products/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(productData)
    })
    .then(res => {
        if (!res.ok) throw new Error("Failed to save");
        return res.json();
    })
    .then(data => {
        ModalSystem.close('product-loading-modal');
        ModalSystem.open('product-success-modal');
    })
    .catch(err => {
        alert("Save failed");
        console.error(err);
    });
 
}

function closeProductSuccessModal() {
    ModalSystem.close('product-success-modal');
    // Refresh the page or update the table in a real app
    window.location.reload();
}

// Update the existing addNewProduct function to use our modal
function addNewProduct() {
    document.getElementById('add-product-modal').showModal();
}



// Product filtering functionality
function filterProducts() {
    const searchTerm = document.getElementById('search-products').value.toLowerCase();
    const statusFilter = document.getElementById('filter-status').value.toLowerCase();
    
    const rows = document.querySelectorAll('.product-row');
    let visibleCount = 0;

    rows.forEach(row => {
        const productText = row.textContent.toLowerCase();
        const name = row.getAttribute('data-name').toLowerCase();
        const tagline = row.getAttribute('data-tagline').toLowerCase();
        const description = row.getAttribute('data-description').toLowerCase();
        const status = row.getAttribute('data-status').toLowerCase();
        
        const matchesSearch = searchTerm === '' || 
                            productText.includes(searchTerm) || 
                            name.includes(searchTerm) ||
                            tagline.includes(searchTerm) ||
                            description.includes(searchTerm);
        const matchesStatus = statusFilter === '' || status === statusFilter;

        if (matchesSearch && matchesStatus) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Update empty state
    const noProductsRow = document.getElementById('no-products-row');
    if (visibleCount === 0 && rows.length > 0) {
        if (noProductsRow) noProductsRow.style.display = '';
    } else {
        if (noProductsRow) noProductsRow.style.display = 'none';
    }

    // Update counter
    document.getElementById('product-count').textContent = `${visibleCount} product${visibleCount !== 1 ? 's' : ''}`;
}

function clearProductFilters() {
    document.getElementById('search-products').value = '';
    document.getElementById('filter-status').value = '';
    filterProducts();
}

// Action functions (kept exactly as before)
function editProduct(productId) {
    window.location.href = `/products/${productId}/edit/`;
}



function duplicateProduct(productId) {
    ModalSystem.toast('Product duplicated successfully!', 'success');
    // In a real app, this would make an API call to duplicate the product
}

function viewProduct(productId) {
    // Find the product URL in the table row
    const row = document.querySelector(`tr[onclick="editProduct(${productId})"]`);
    if (row) {
        const urlElement = row.querySelector('a[href^="http"]');
        if (urlElement) {
            window.open(urlElement.href, '_blank');
            return;
        }
    }
    ModalSystem.toast('No landing page URL set for this product', 'warning');
}

function confirmDeleteProduct(productId) {
    ModalSystem.confirm({
        title: 'Delete Product',
        message: 'Are you sure you want to delete this product? This action cannot be undone.',
        confirmText: 'Delete',
        confirmClass: 'btn-error',
        action: function() {
            fetch(`/api/products/${productId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                if (!response.ok) {
                    ModalSystem.toast('Error deleting product', 'error');
                    throw new Error('Failed to delete product');
                }
                const row = document.querySelector(`tr[id="${productId}"]`);
                if (row) row.remove();

                counter= document.getElementById("product-count")
                counter.innerHTML= counter.innerHTML - 1;
                
                
                ModalSystem.toast('Product deleted successfully!', 'success');
                return response;
            })
            .catch(error => {
                console.error('Error deleting product:', error);
                throw error;
            });
        }
    });
}


// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for filters
    document.getElementById('search-products').addEventListener('input', filterProducts);
    document.getElementById('filter-status').addEventListener('change', filterProducts);
    
    // Hide empty state if we have products
    const noProductsRow = document.getElementById('no-products-row');
    if (noProductsRow && document.querySelectorAll('.product-row').length > 0) {
        noProductsRow.style.display = 'none';
    }
});
</script>
{% endblock %}