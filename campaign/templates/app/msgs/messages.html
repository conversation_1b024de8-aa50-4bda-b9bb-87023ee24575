{% extends "base-app.html" %}

{% block title %}Messages{% endblock %}

{% block content %}
<div class="flex-1 flex flex-col overflow-hidden bg-gray-50 p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Messages</h1>
                <p class="text-gray-600">Manage email templates and view performance analytics</p>
            </div>
            <div class="flex gap-2">
                <button class="btn btn-primary" onclick="addNewMessage()">
                    <i class="fa-solid fa-plus mr-2"></i>
                    New Message
                </button>
            </div>
        </div>
    </div>

        <div class="stats shadow mb-6  min-h-[140px]">
    <div class="stat">
        <div class="stat-figure text-secondary">
            <i class="fa-solid fa-envelope-open text-3xl text-blue-600"></i>
        </div>
        <div class="stat-title">Most Opened</div>
        <div class="stat-value">{{most_opened.open_rate}}%</div>
        <div class="stat-desc">{{most_opened.subject}}</div>
    </div>

    <div class="stat">
        <div class="stat-figure text-secondary">
         <i class="fa-solid fa-mouse-pointer text-green-600 text-3xl"></i>
        </div>
        <div class="stat-title">Most Clicked (have a link)</div>   
        <div class="stat-value">{{most_clicked.ctr}}%</div>
        <div class="stat-desc">{{most_clicked.subject}}</div>
    </div>

    <div class="stat">
        <div class="stat-figure text-secondary">
        <i class="fa-solid fa-star text-purple-600 text-3xl"></i>
        </div>
        <div class="stat-title">Most Replied</div>
        <div class="stat-value">{{most_replied.responded_rate}}%</div>
        <div class="stat-desc">{{most_replied.subject}}</div>
    </div>
    
     <div class="stat">
        <div class="stat-figure text-secondary">
        <i class="fa-solid fa-chart-line text-orange-600 text-3xl"></i>
        </div>
        <div class="stat-title">Most Opportunities (generated)</div>
        <div class="stat-value">{{most_opportunity.opportunity_count}}%</div>
        <div class="stat-desc">{{most_opportunity.subject}}</div>
    </div>
    
    </div>
     


    <!-- Filters and Search -->
    <div class="mb-6 bg-white rounded-lg p-4 shadow-sm border border-gray-200">
        <div class="flex flex-wrap gap-4 items-center">
            <div class="flex-1 min-w-64">
                <input type="text"
                       placeholder="Search messages by subject, content..."
                       class="input input-bordered w-full bg-white"
                       id="search-messages">
            </div>
            <button onclick="clearMessageFilters()" class="btn btn-ghost">Clear</button>
            <select class="select select-bordered bg-white" id="filter-product">
                <option value="">All Products</option>
                {% for product in products %}
                <option value="{{ product.name|lower }}">{{ product.name }}</option>
                {% endfor %}
            </select>
            <select class="select select-bordered bg-white" id="filter-sort">
                <option value="">Sort By</option>
                <option value="name">Name</option>
                <option value="performance">Performance</option>
                <option value="open-rate">Open Rate</option>
                <option value="click-rate">Click Rate</option>
            </select>
            <div class="text-sm text-gray-500">
                <span id="message-count">{{ messages|length }}</span> messages
            </div>
        </div>
    </div>

    <!-- Messages Table -->
    <div class="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="table w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="text-left font-semibold text-gray-900">Subject</th>
                        <th class="text-left font-semibold text-gray-900">Product</th>
                        <th class="text-left font-semibold text-gray-900">Performance</th>
                        <th class="text-left font-semibold text-gray-900">Open Rate</th>
                        <th class="text-left font-semibold text-gray-900">Reply Rate</th>
                        <th class="text-left font-semibold text-gray-900">Actions</th>
                    </tr>
                </thead>
                <tbody id="messages-table-body">
                    {% for message in messages %}
                    <tr class="hover:bg-gray-50 cursor-pointer message-row" 
                        onclick="editMessage({{ message.id }})"
                        data-product="{{ message.product|lower }}"
                        data-performance="{{ message.performance }}"
                        data-open-rate="{{ message.open_rate }}"
                        data-click-rate="{{ message.click_rate }}"
                        data-name="{{ message.subject|lower }}">
                        <td>
                            <div class="flex items-center gap-3">
                                <div class="w-2 h-2 
                                    {% if message.performance >= 70 %}bg-green-500
                                    {% elif message.performance >= 40 %}bg-yellow-500
                                    {% else %}bg-red-500{% endif %} 
                                    rounded-full" 
                                    title="{% if message.performance >= 70 %}High performing
                                    {% elif message.performance >= 40 %}Medium performing
                                    {% else %}Low performing{% endif %}">
                                </div>
                                <div title="{{message.intro}}">
                                    <div class="font-medium text-gray-900">{{ message.subject }}</div>
                                    <div class="text-sm text-gray-500">{{ message.intro|truncatechars:40 }}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge badge-ghost badge-sm">{{ message.product }}</span>
                        </td>
                         <td>
                            <div class="flex items-center gap-2">
                                <div class="w-16 bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: {{message.performance}}%"></div>
                                </div>
                                <span class="text-sm font-medium text-green-600" title="Performance metric (opened + replied + clicked through) / 3">{{message.performance}}%</span>
                            </div>
                        </td>
                        <td>
                            <span class="text-sm font-medium text-gray-900">{{message.open_rate}}%</span>
                        </td>
                        <td>
                            <span class="text-sm font-medium text-gray-900">{{message.responded_rate}}%</span>
                        </td>
                        <td>
                            <div class="flex items-center gap-2">
                                <button class="btn btn-ghost btn-xs" onclick="event.stopPropagation(); duplicateMessage({{ message.id }})" title="Duplicate">
                                    <i class="fa-solid fa-copy"></i>
                                </button>
                                <button class="btn btn-ghost btn-xs" onclick="event.stopPropagation(); editMessage({{ message.id }})" title="Preview">
                                    <i class="fa-regular fa-eye"></i>
                                </button>
                                <button class="btn btn-ghost btn-xs text-red-500" onclick="event.stopPropagation(); confirmDeleteMessage({{ message.id }})" title="Delete">
                                    <i class="fa-solid fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr id="no-messages-row">
                        <td colspan="6" class="text-center py-16">
                            <div class="flex flex-col items-center justify-center">
                                <img src="https://raw.githubusercontent.com/Othunderlight/static/refs/heads/main/no-messages.svg"
                                     alt="No messages illustration"
                                     class="w-64 h-auto mx-auto">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2 mt-4">
                                    👋 Create your first message
                                </h3>
                                <p class="text-gray-500 mb-6">
                                    Get started by creating a new email message template
                                </p>
                                <button class="btn btn-primary" onclick="addNewMessage()">
                                    <i class="fa-solid fa-plus mr-2"></i>
                                    New Message
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Message filtering and sorting functionality
function filterMessages() {
    const searchTerm = document.getElementById('search-messages').value.toLowerCase();
    const productFilter = document.getElementById('filter-product').value.toLowerCase();
    const sortFilter = document.getElementById('filter-sort').value;
    
    const rows = document.querySelectorAll('.message-row');
    let visibleCount = 0;

    // First filter the rows
    rows.forEach(row => {
        const messageText = row.textContent.toLowerCase();
        const product = row.getAttribute('data-product').toLowerCase();
        const name = row.getAttribute('data-name').toLowerCase();
        
        const matchesSearch = searchTerm === '' || 
                             messageText.includes(searchTerm) || 
                             name.includes(searchTerm);
        const matchesProduct = productFilter === '' || product === productFilter;

        if (matchesSearch && matchesProduct) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Then sort if a sort option is selected
    if (sortFilter) {
        sortTable(sortFilter);
    }

    // Update empty state
    const noMessagesRow = document.getElementById('no-messages-row');
    if (visibleCount === 0 && rows.length > 0) {
        noMessagesRow.style.display = '';
    } else {
        noMessagesRow.style.display = 'none';
    }

    // Update counter
    document.getElementById('message-count').textContent = `${visibleCount} messages`;
}

function sortTable(sortBy) {
    const tbody = document.getElementById('messages-table-body');
    const rows = Array.from(tbody.querySelectorAll('.message-row[style=""]'));
    
    rows.sort((a, b) => {
        let aValue, bValue;
        
        switch(sortBy) {
            case 'name':
                aValue = a.getAttribute('data-name');
                bValue = b.getAttribute('data-name');
                return aValue.localeCompare(bValue);
            case 'performance':
                aValue = parseFloat(a.getAttribute('data-performance'));
                bValue = parseFloat(b.getAttribute('data-performance'));
                return bValue - aValue; // Descending order
            case 'open-rate':
                aValue = parseFloat(a.getAttribute('data-open-rate'));
                bValue = parseFloat(b.getAttribute('data-open-rate'));
                return bValue - aValue;
            case 'click-rate':
                aValue = parseFloat(a.getAttribute('data-click-rate'));
                bValue = parseFloat(b.getAttribute('data-click-rate'));
                return bValue - aValue;
            default:
                return 0;
        }
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}

function clearMessageFilters() {
    document.getElementById('search-messages').value = '';
    document.getElementById('filter-product').value = '';
    document.getElementById('filter-sort').value = '';
    filterMessages();
}

// Action functions
function editMessage(id) {
    window.location.href = `/campaign/messages/edit/${id}`;
}

function addNewMessage() {
    window.location.href = '/campaign/messages/add/?add_new=true';
}

function duplicateMessage(id) {
    ModalSystem.toast('Message duplicated successfully!', 'success');
    // In a real app, this would make an API call to duplicate the message
}

function previewMessage(id) {
    ModalSystem.toast('Preview functionality coming soon!', 'info');
    // In a real app, this would open a modal with message preview
}

function confirmDeleteMessage(id) {
    ModalSystem.confirm({
        title: 'Delete Message',
        message: 'Are you sure you want to delete this message template? This action cannot be undone.',
        confirmText: 'Delete',
        confirmClass: 'btn-error',
        action: function() {
            fetch(`/api/messages/${id}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                if (!response.ok) {
                    ModalSystem.toast('Error deleting message', 'error');
                    throw new Error('Failed to delete message');
                }
                const row = document.querySelector(`tr[onclick="editMessage(${id})"]`);
                if (row) row.remove();

                counter= document.getElementById("message-count")
                counter.innerHTML= counter.innerHTML - 1;
                
                
                ModalSystem.toast('Message deleted successfully!', 'success');
                return response;
            })
            .catch(error => {
                console.error('Error deleting message:', error);
                throw error;
            });
        }
    });
}


// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for filters
    document.getElementById('search-messages').addEventListener('input', filterMessages);
    document.getElementById('filter-product').addEventListener('change', filterMessages);
    document.getElementById('filter-sort').addEventListener('change', function() {
        filterMessages(); // This will trigger sorting too
    });
    
    // Hide empty state if we have messages
    const noMessagesRow = document.getElementById('no-messages-row');
    if (noMessagesRow && document.querySelectorAll('.message-row').length > 0) {
        noMessagesRow.style.display = 'none';
    }
});
</script>
{% endblock %}