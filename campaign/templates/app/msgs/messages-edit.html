{% extends "base-app.html" %}

{% block title %}Edit Messages{% endblock %}

{% block content %}
<style>
/* Custom styles for messages */
.message-row {
    cursor: pointer;
    transition: all 0.2s ease;
}

.message-row:hover {
    background-color: #f8fafc;
}

.message-row.active-message {
    background-color: #eff6ff;
    border-left: 4px solid #3b82f6;
}

#message-preview {
    display: none;
    padding: 2rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: #333;
}

#message-preview h1,
#message-preview h2,
#message-preview h3 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

#message-preview p {
    margin-bottom: 1rem;
}

#message-preview a {
    color: #3b82f6;
    text-decoration: underline;
}

#message-preview .btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: #3b82f6;
    color: white;
    border-radius: 0.375rem;
    text-decoration: none;
    margin: 1rem 0;
}

#message-preview .signature {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

#edit-preview-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

/* Search and filter styles */
#search-messages {
    transition: all 0.3s ease;
}

#search-messages:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

#filter-type {
    min-width: 120px;
}
</style>

<div class="flex-1 flex flex-col overflow-hidden bg-gray-50 p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div class="flex items-center gap-4">
                <button class="btn btn-ghost btn-sm" onclick="window.history.back()">
                    <i class="fa-solid fa-arrow-left mr-2"></i>
                    Back to Messages
                </button> 
            </div>
            <button class="btn btn-primary" onclick="addNewMessage()">
                <i class="fa-solid fa-plus mr-2"></i>
                New Message
            </button>
        </div>
    </div>

    <!-- Two-column layout -->
    <div class="flex-1 flex gap-6 overflow-hidden">
        <!-- Left Column: Messages Table -->
        <div class="w-full max-w-lg flex-shrink-0 flex flex-col bg-white border border-base-200 rounded-lg overflow-hidden">
            <!-- Table Header -->
            <div class="p-4 border-b border-base-200 bg-gray-50">
                <div class="flex justify-between items-center">
                    <h3 class="font-semibold text-gray-900">Message Templates</h3>
                    <span class="text-sm text-gray-500" id="message-count">
                        {% if messages %}
                            {{ messages|length }} message{{ messages|length|pluralize }}
                        {% else %}
                            No messages
                        {% endif %}
                    </span>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="p-4 border-b border-base-200">
                <div class="flex gap-2">
                    <input type="text" 
                           placeholder="Search messages..." 
                           class="input input-bordered input-sm flex-1 bg-white"
                           id="search-messages"
                           oninput="filterMessages()">
                    <select class="select select-bordered select-sm bg-white" id="filter-product" onchange="filterMessages()">
                        <option value="">All Products</option>
                        {% for product in products %}
                        <option value="{{ product.id }}">{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <!-- Messages Table -->
            <div class="flex-1 overflow-y-auto" id="messages-container">
                {% if messages %}
                    {% for message in messages %}
                    <div class="message-row p-4 border-b border-base-200 {% if message.id == message_id %}active-message
                                {% elif not message_id and forloop.first %}active-message
                                {% endif %}" 
                         data-message="{{ message.id }}"
                         data-product="{{ message.product.id }}"
                         onclick="selectMessage({{ message.id }}, true)">
                        <div class="flex justify-between items-start">
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium text-gray-900 truncate">{{ message.subject }}</h4>
                                <p class="text-sm text-gray-500 mt-1 line-clamp-2">
                                    {{ message.intro|truncatechars:60 }}
                                </p>
                                <div class="flex items-center gap-4 mt-2">
                                    <span class="text-xs text-gray-400">{{ message.product.name }}</span>
                                    <span class="text-xs text-gray-400">Updated: {{ message.updated_at|timesince }} ago</span>
                                </div>
                            </div>
                            <div class="flex items-center gap-2 ml-2">
                                <button class="btn btn-ghost btn-xs" onclick="event.stopPropagation(); duplicateMessage({{ message.id }})" title="Duplicate">
                                    <i class="fa-solid fa-copy"></i>
                                </button>
                                <button class="btn btn-ghost btn-xs text-red-500" onclick="event.stopPropagation(); confirmDeleteMessage({{ message.id }})" title="Delete">
                                    <i class="fa-solid fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="p-8 text-center">
                        <div class="text-gray-400 mb-4">
                            <i class="fas fa-envelope-open-text fa-3x"></i>
                        </div>
                        <h3 class="text-lg font-medium mb-2">No messages found</h3>
                        <p class="text-gray-500 mb-4">Create your first message template to get started</p>
                        <button class="btn btn-primary" onclick="addNewMessage()">
                            <i class="fa-solid fa-plus mr-2"></i> Create Message
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Right Column: Message Editor/Preview -->
        <div class="flex-1 flex flex-col bg-white border border-base-200 rounded-lg h-[calc(100vh-180px)] relative">
            {% if messages %}
                <!-- Show preview by default if messages exist -->
                <div id="message-preview" class="flex-1 overflow-y-auto">
                    <button id="edit-preview-btn" class="btn btn-sm btn-primary" onclick="showEditor()">
                        <i class="fa-solid fa-edit mr-2"></i>Edit
                    </button>
                    {% for message in messages %}
                    <div id="preview-content-{{ message.id }}" class="message-preview-content" style="display: {% if message.id == message_id %}block{% elif not message_id and forloop.first %}block{% else %}none{% endif %}">
                        {{ message.full_content|safe }}
                    </div>
                    {% endfor %}
                </div>
            {% endif %}

            <!-- Message Form -->
            <div id="message-form-container" class="flex-grow overflow-y-auto" style="display: {% if messages %}none{% else %}block{% endif %}">
                <form id="message-form" class="p-4 space-y-4">
                    <!-- Header -->
                    <div class="flex justify-between items-center pb-4 border-b border-base-200">
                        <div class="text-sm">
                            <span class="font-bold text-gray-800">Subject</span>
                            <span class="text-gray-500 ml-2" id="subject-display">
                                {% if messages %}{{ messages.0.subject }}{% else %}New Message{% endif %}
                            </span>
                        </div>
                        <div class="flex items-center gap-2">
                            <button type="button" class="btn btn-sm bg-white border border-base-300" onclick="previewMessage()">
                                <i class="fa-regular fa-eye mr-2"></i>Preview
                            </button>
                            <button class="btn btn-sm btn-square bg-white border border-base-300" title="Variables">
                                <i class="fa-solid fa-bolt text-blue-500"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Product -->
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Product</span>
                        </label>
                        <select class="select select-bordered w-full bg-white" id="message-product" name="product">
                            {% for product in products %}
                            <option value="{{ product.id }}" {% if messages and messages.0.product.id == product.id %}selected{% endif %}>
                                {{ product.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Subject -->
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Subject</span>
                        </label>
                        <input type="text"
                               placeholder="Your subject"
                               class="input input-bordered w-full bg-white"
                               id="message-subject"
                               name="subject"
                               value="{% if messages %}{{ messages.0.subject }}{% endif %}">
                    </div>

                    <!-- Intro -->
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Intro</span>
                        </label>
                        <textarea class="textarea textarea-bordered w-full bg-white"
                                  rows="3"
                                  placeholder="Start typing here..."
                                  id="message-intro"
                                  name="intro">{% if messages %}{{ messages.0.intro }}{% endif %}</textarea>
                    </div>

                    <!-- Content -->
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Content</span>
                        </label>
                        <textarea class="textarea textarea-bordered w-full bg-white"
                                  rows="6"
                                  placeholder="Start typing here..."
                                  id="message-content"
                                  name="content">{% if messages %}{{ messages.0.content }}{% endif %}</textarea>
                    </div>

                    <!-- CTA -->
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Call to Action</span>
                        </label>
                        <input type="text"
                               placeholder="Your call to action"
                               class="input input-bordered w-full bg-white"
                               id="message-cta"
                               name="cta"
                               value="{% if messages %}{{ messages.0.cta }}{% endif %}">
                    </div>

                    <!-- P.S -->
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">P.S</span>
                        </label>
                        <textarea class="textarea textarea-bordered w-full bg-white"
                                  rows="2"
                                  placeholder="P.S. message..."
                                  id="message-ps"
                                  name="ps">{% if messages %}{{ messages.0.ps }}{% endif %}</textarea>
                    </div>

                    <!-- P.P.S -->
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">P.P.S</span>
                        </label>
                        <textarea class="textarea textarea-bordered w-full bg-white"
                                  rows="2"
                                  placeholder="P.P.S. message..."
                                  id="message-pps"
                                  name="pps">{% if messages %}{{ messages.0.pps }}{% endif %}</textarea>
                    </div>

                    <!-- End/Signature -->
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Signature</span>
                        </label>
                        <textarea class="textarea textarea-bordered w-full bg-white"
                                  rows="3"
                                  placeholder="Best regards,&#10;Your name"
                                  id="message-end"
                                  name="end">{% if messages %}{{ messages.0.end }}{% endif %}</textarea>
                    </div>
                </form>
            </div>

            <!-- Bottom Toolbar -->
            <div class="p-4 border-t border-base-200">
                <div class="flex justify-between items-center">
                    <div class="flex items-center gap-2">
                        <button class="btn btn-sm btn-primary" onclick="saveMessage(activeMessage)">
                            Save
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="showTemplates()">
                            <i class="fa-solid fa-file-text mr-2"></i>Templates
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="showVariables()">
                            <i class="fa-solid fa-code mr-2"></i>Variables
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="showAI()">
                            <i class="fa-solid fa-magic mr-2"></i>AI
                        </button>
                    </div>
                    <div class="flex items-center gap-2">
                        <button class="btn btn-sm btn-ghost" onclick="copyMessage()">
                            <i class="fa-solid fa-copy mr-2"></i>Copy
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="exportMessage()">
                            <i class="fa-solid fa-download mr-2"></i>Export
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>  
// Message Management System
let activeMessage = {% if message_id %}{{ message_id }}{% elif messages %}{{ messages.0.id }}{% else %}null{% endif %};

let messages = {};

// Initialize messages from Django template if they exist
{% if messages %}
    {% for message in messages %}
    messages[{{ message.id }}] = {
        id: {{ message.id }},
        product_id: {{ message.product.id }},
        product_name: "{{ message.product.name|escapejs }}",
        subject: "{{ message.subject|escapejs }}",
        intro: "{{ message.intro|escapejs }}",
        content: "{{ message.content|escapejs }}",
        cta: "{{ message.cta|escapejs }}",
        ps: "{{ message.ps|escapejs }}",
        pps: "{{ message.pps|escapejs }}",
        end: "{{ message.end|escapejs }}",
        full_content: `{{ message.full_content|safe|escapejs }}`,
        updated_at: "{{ message.updated_at|date:'Y-m-d' }}"
    };
    {% endfor %}
{% endif %}

function selectMessage(messageId, scrollIntoView = false) {
    // Remove active class from all messages
    document.querySelectorAll('.message-row').forEach(row => {
        row.classList.remove('active-message');
    });

    // Add active class to selected message
    const selectedRow = document.querySelector(`[data-message="${messageId}"]`);
    if (selectedRow) {
        selectedRow.classList.add('active-message');
        if (scrollIntoView) {
            selectedRow.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
        activeMessage = messageId;

        // Load message content
        loadMessageContent(messageId);
        
        // Show the corresponding preview content
        document.querySelectorAll('.message-preview-content').forEach(preview => {
            preview.style.display = 'none';
        });
        const previewContent = document.getElementById(`preview-content-${messageId}`);
        if (previewContent) {
            previewContent.style.display = 'block';
        }
    }
}

function loadMessageContent(messageId) {
    const message = messages[messageId] || {};

    // Load message data into form
    document.getElementById('message-product').value = message.product_id || '';
    document.getElementById('message-subject').value = message.subject || '';
    document.getElementById('message-intro').value = message.intro || '';
    document.getElementById('message-content').value = message.content || '';
    document.getElementById('message-cta').value = message.cta || '';
    document.getElementById('message-ps').value = message.ps || '';
    document.getElementById('message-pps').value = message.pps || '';
    document.getElementById('message-end').value = message.end || '';

    // Update subject display
    document.getElementById('subject-display').textContent = message.subject || 'New Message';
}

function saveCurrentMessage(messageId) {
    if (!messageId) return saveNewMessage();

    // Get current values from the form
    const currentValues = {
        product: document.getElementById('message-product').value,
        subject: document.getElementById('message-subject').value,
        intro: document.getElementById('message-intro').value,
        content: document.getElementById('message-content').value,
        cta: document.getElementById('message-cta').value,
        ps: document.getElementById('message-ps').value,
        pps: document.getElementById('message-pps').value,
        end: document.getElementById('message-end').value,
    };

    // Get original values
    const originalValues = messages[activeMessage] || {};

    // Determine changed fields
    const changedFields = {};
    let changedCount = 0;

    for (const key in currentValues) {
        if (currentValues[key] !== originalValues[key]) {
            changedFields[key] = currentValues[key];
            changedCount++;
        }
    }

    if (changedCount === 0) {
        return ModalSystem.toast("No changes detected", "warning");
    }

    // BEST PRACTICE IMPLEMENTATION:
    const isCompleteUpdate = changedCount === Object.keys(currentValues).length;
    const method = isCompleteUpdate ? 'PUT' : 'PATCH';
    const dataToSend = isCompleteUpdate ? currentValues : changedFields;

    fetch(`/api/messages/${messageId}/`, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken'),
        },
        body: JSON.stringify(dataToSend),
    })
    .then(response => {
        if (!response.ok) throw new Error('Failed to save message');
        return response.json();
    })
    .then(data => {
        // Update local state
        messages[messageId] = {
            ...messages[messageId],
            ...currentValues,
            product_id: currentValues.product,
            product_name: document.getElementById('message-product').options[document.getElementById('message-product').selectedIndex].text,
            updated_at: new Date().toISOString()
        };
        
        updateMessageRow(messageId, messages[messageId]);
        ModalSystem.toast(`Message saved (via ${method})`, 'success');
    })
    .catch(error => {
        console.error('Error saving message:', error);
        ModalSystem.toast('Error saving message', 'error');
    });
}


function saveNewMessage() {
    // Get values from the form
    const newMessage = {
        product: document.getElementById('message-product').value,
        subject: document.getElementById('message-subject').value,
        intro: document.getElementById('message-intro').value,
        content: document.getElementById('message-content').value,
        cta: document.getElementById('message-cta').value,
        ps: document.getElementById('message-ps').value,
        pps: document.getElementById('message-pps').value,
        end: document.getElementById('message-end').value,
    };

    // Make the API request to create new message
    fetch('/api/messages/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken'),
        },
        body: JSON.stringify(newMessage),
    })
    .then(response => {
        if (!response.ok) throw new Error('Failed to create message');
        return response.json();
    })
    .then(data => {
        // Add the new message to local storage
        messages[data.id] = {
            id: data.id,
            product_id: data.product.id,
            product_name: data.product.name,
            subject: data.subject,
            intro: data.intro,
            content: data.content,
            cta: data.cta,
            ps: data.ps,
            pps: data.pps,
            end: data.end,
            full_content: data.full_content,
            updated_at: data.updated_at
        };

        // Create the new message row in the UI
        const newMessageHTML = `
            <div class="message-row p-4 border-b border-base-200 active-message" 
                 data-message="${data.id}" 
                 data-product="${data.product.id}"
                 onclick="selectMessage(${data.id}, true)">
                <div class="flex justify-between items-start">
                    <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-gray-900 truncate">${data.subject}</h4>
                        <p class="text-sm text-gray-500 mt-1 line-clamp-2">${data.intro.substring(0, 60)}${data.intro.length > 60 ? '...' : ''}</p>
                        <div class="flex items-center gap-4 mt-2">
                            <span class="text-xs text-gray-400">${data.product.name}</span>
                            <span class="text-xs text-gray-400">Updated: Just now</span>
                        </div>
                    </div>
                    <div class="flex items-center gap-2 ml-2">
                        <button class="btn btn-ghost btn-xs" onclick="event.stopPropagation(); duplicateMessage(${data.id})" title="Duplicate">
                            <i class="fa-solid fa-copy"></i>
                        </button>
                        <button class="btn btn-ghost btn-xs text-red-500" onclick="event.stopPropagation(); confirmDeleteMessage(${data.id})" title="Delete">
                            <i class="fa-solid fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add to the UI and select it
        const messagesContainer = document.getElementById('messages-container');
        messagesContainer.insertAdjacentHTML('afterbegin', newMessageHTML);
        
        // Update message count
        const messageCount = Object.keys(messages).length;
        document.getElementById('message-count').textContent = `${messageCount} message${messageCount !== 1 ? 's' : ''}`;
        
        // Select the new message
        selectMessage(data.id);
        ModalSystem.toast('New message created successfully!', 'success');
    })
    .catch(error => {
        console.error('Error creating message:', error);
        ModalSystem.toast('Error creating message', 'error');
    });
}

function updateMessageRow(messageId, messageData) {
    const messageRow = document.querySelector(`[data-message="${messageId}"]`);
    if (messageRow) {
        // Update the data-product attribute
        messageRow.setAttribute('data-product', messageData.product_id);
        
        // Update the visible content
        const titleElement = messageRow.querySelector('h4');
        const descElement = messageRow.querySelector('p');
        const productElement = messageRow.querySelector('.text-xs:first-child');
        const dateElement = messageRow.querySelector('.text-xs:last-child');
        
        if (titleElement) titleElement.textContent = messageData.subject || 'Untitled Message';
        if (descElement) descElement.textContent = messageData.intro?.substring(0, 60) + (messageData.intro?.length > 60 ? '...' : '') || 'No content';
        if (productElement) productElement.textContent = messageData.product_name || 'No product';
        if (dateElement) dateElement.textContent = `Updated: ${timeSince(new Date(messageData.updated_at))} ago`;
    }
}

function timeSince(date) {
    const seconds = Math.floor((new Date() - date) / 1000);
    let interval = Math.floor(seconds / 31536000);
    
    if (interval >= 1) return interval + " year" + (interval === 1 ? "" : "s");
    interval = Math.floor(seconds / 2592000);
    if (interval >= 1) return interval + " month" + (interval === 1 ? "" : "s");
    interval = Math.floor(seconds / 86400);
    if (interval >= 1) return interval + " day" + (interval === 1 ? "" : "s");
    interval = Math.floor(seconds / 3600);
    if (interval >= 1) return interval + " hour" + (interval === 1 ? "" : "s");
    interval = Math.floor(seconds / 60);
    if (interval >= 1) return interval + " minute" + (interval === 1 ? "" : "s");
    return Math.floor(seconds) + " second" + (seconds === 1 ? "" : "s");
}

function showPreview() {
    document.getElementById('message-form-container').style.display = 'none';
    document.getElementById('message-preview').style.display = 'block';
}

function showEditor() {
    document.getElementById('message-preview').style.display = 'none';
    document.getElementById('message-form-container').style.display = 'block';
}

function previewMessage() {
    // Generate HTML preview (simplified version)
    const message = messages[activeMessage] || {
        subject: document.getElementById('message-subject').value,
        intro: document.getElementById('message-intro').value,
        content: document.getElementById('message-content').value,
        cta: document.getElementById('message-cta').value,
        ps: document.getElementById('message-ps').value,
        pps: document.getElementById('message-pps').value,
        end: document.getElementById('message-end').value
    };
    
    const previewContent = `
        <h1>${message.subject || 'No subject'}</h1>
        ${message.intro ? `<p>${message.intro.replace(/\n/g, '<br>')}</p>` : ''}
        ${message.content ? `<div>${message.content.replace(/\n/g, '<br>')}</div>` : ''}
        ${message.cta ? `<a href="#" class="btn">${message.cta}</a>` : ''}
        ${message.ps ? `<p><strong>P.S.</strong> ${message.ps.replace(/\n/g, '<br>')}</p>` : ''}
        ${message.pps ? `<p><strong>P.P.S.</strong> ${message.pps.replace(/\n/g, '<br>')}</p>` : ''}
        ${message.end ? `<div class="signature">${message.end.replace(/\n/g, '<br>')}</div>` : ''}
    `;
    
    // Update the preview content
    if (activeMessage) {
        messages[activeMessage].full_content = previewContent;
    }
    
    // Show the preview
    showPreview();
    
    // Create or update the preview content div
    let previewDiv = document.getElementById(`preview-content-${activeMessage || 'new'}`);
    if (!previewDiv) {
        previewDiv = document.createElement('div');
        previewDiv.id = `preview-content-${activeMessage || 'new'}`;
        previewDiv.className = 'message-preview-content';
        document.getElementById('message-preview').appendChild(previewDiv);
    }
    previewDiv.innerHTML = previewContent;
}

function filterMessages() {
    const searchTerm = document.getElementById('search-messages').value.toLowerCase();
    const productFilter = document.getElementById('filter-product').value;
    
    document.querySelectorAll('.message-row').forEach(row => {
        const messageId = row.getAttribute('data-message');
        const message = messages[messageId];
        const matchesSearch = searchTerm === '' || 
            (message.subject && message.subject.toLowerCase().includes(searchTerm)) ||
            (message.intro && message.intro.toLowerCase().includes(searchTerm)) ||
            (message.content && message.content.toLowerCase().includes(searchTerm));
        
        const matchesProduct = productFilter === '' || 
            (message.product_id && message.product_id.toString() === productFilter);
        
        if (matchesSearch && matchesProduct) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
    
    // Update message count display
    const visibleCount = document.querySelectorAll('.message-row:not([style*="display: none"])').length;
    document.getElementById('message-count').textContent = `${visibleCount} message${visibleCount !== 1 ? 's' : ''}`;
}

function addNewMessage() {
    const productSelect = document.getElementById('filter-product');
    const productId = productSelect?.value || '';
    const productName = productSelect?.options[productSelect.selectedIndex]?.text || 'Unknown Product';

    // Clear the form
    document.getElementById('message-subject').value = 'New Message Template';
    document.getElementById('message-intro').value = '';
    document.getElementById('message-content').value = '';
    document.getElementById('message-cta').value = '';
    document.getElementById('message-ps').value = '';
    document.getElementById('message-pps').value = '';
    document.getElementById('message-end').value = '';
    document.getElementById('subject-display').textContent = 'New Message Template';

    // Show the editor
    showEditor();

    // Set activeMessage to null for new message
    activeMessage = null;

    // Remove active class from all messages
    document.querySelectorAll('.message-row').forEach(row => {
        row.classList.remove('active-message');
    });

    window.ModalSystem?.toast?.('New message template created! Fill in the details and click Save.');
}

function duplicateMessage(messageId) {
    const message = messages[messageId];
    if (!message) return;
    
    // In a real app, you would make an AJAX call to duplicate the message
    const newMessageId = Date.now(); // Temporary ID
    messages[newMessageId] = {
        ...message,
        id: newMessageId,
        subject: `Copy of ${message.subject}`,
        updated_at: new Date().toISOString().split('T')[0]
    };
    
    // Add to the UI
    const newMessageHTML = `
        <div class="message-row p-4 border-b border-base-200" 
             data-message="${newMessageId}" 
             data-product="${message.product_id}"
             onclick="selectMessage(${newMessageId}, true)">
            <div class="flex justify-between items-start">
                <div class="flex-1 min-w-0">
                    <h4 class="font-medium text-gray-900 truncate">Copy of ${message.subject}</h4>
                    <p class="text-sm text-gray-500 mt-1 line-clamp-2">${message.intro?.substring(0, 60) || 'No content'}${message.intro?.length > 60 ? '...' : ''}</p>
                    <div class="flex items-center gap-4 mt-2">
                        <span class="text-xs text-gray-400">${message.product_name}</span>
                        <span class="text-xs text-gray-400">Updated: Just now</span>
                    </div>
                </div>
                <div class="flex items-center gap-2 ml-2">
                    <button class="btn btn-ghost btn-xs" onclick="event.stopPropagation(); duplicateMessage(${newMessageId})" title="Duplicate">
                        <i class="fa-solid fa-copy"></i>
                    </button>
                    <button class="btn btn-ghost btn-xs text-red-500" onclick="event.stopPropagation(); confirmDeleteMessage(${newMessageId})" title="Delete">
                        <i class="fa-solid fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    const messagesContainer = document.getElementById('messages-container');
    messagesContainer.insertAdjacentHTML('afterbegin', newMessageHTML);
    
    // Select the new message
    selectMessage(newMessageId);
    
    window.ModalSystem.toast('Message duplicated successfully!');
}

function confirmDeleteMessage(messageId) {
    window.ModalSystem.confirm({
        title: 'Delete Message',
        message: 'Are you sure you want to delete this message template? This action cannot be undone.',
        confirmText: 'Delete',
        confirmClass: 'btn-error',
        action: function() {
            fetch(`/api/messages/${messageId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                if (!response.ok) {
                    ModalSystem.toast('Error deleting message', 'error');
                    throw new Error('Failed to delete message');
                }
            
                // Remove from UI
                const messageRow = document.querySelector(`[data-message="${messageId}"]`);
                if (messageRow) {
                    messageRow.remove();
                    
                    // Update message count
                    const remainingCount = document.querySelectorAll('.message-row').length;
                    document.getElementById('message-count').textContent = `${remainingCount} message${remainingCount !== 1 ? 's' : ''}`;
                    
                    if (activeMessage === messageId) {
                        // Select another message if available
                        const firstMessageRow = document.querySelector('.message-row');
                        if (firstMessageRow) {
                            selectMessage(parseInt(firstMessageRow.getAttribute('data-message')));
                        } else {
                            // No messages left
                            activeMessage = null;
                            document.getElementById('message-form-container').style.display = 'block';
                            document.getElementById('message-preview').style.display = 'none';
                        }
                    }
                }
                
                ModalSystem.toast('Message deleted successfully!');
            });
        }
    });
}

function saveMessage(messageId) {
    if (messageId) {
        saveCurrentMessage(messageId);
    } else {
        saveNewMessage();
    }
}


// Initialize
document.addEventListener('DOMContentLoaded', function() {
    {% if messages %}
        selectMessage({{ message_id|default:messages.0.id }});
        showPreview();
    {% endif %}

    // Check URL param to auto-add new message
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('add_new') === 'true') {
        addNewMessage();
        // Remove 'run' from URL so it doesn't keep triggering
        urlParams.delete('run');
        window.history.replaceState({}, document.title, window.location.pathname);
    }

    // Update subject display when typing in subject field
    document.getElementById('message-subject').addEventListener('input', function() {
        document.getElementById('subject-display').textContent = this.value || 'New Message';
    });
});

// Placeholder functions for unimplemented features
function showTemplates() {
    ModalSystem.toast('Templates feature coming soon!', 'info');
}

function showVariables() {
    ModalSystem.toast('Variables feature coming soon!', 'info');
}

function showAI() {
    ModalSystem.toast('AI feature coming soon!', 'info');
}

function copyMessage() {
    ModalSystem.toast('Copy feature coming soon!', 'info');
}

function exportMessage() {
    ModalSystem.toast('Export feature coming soon!', 'info');
}
</script>
{% endblock %}