{% extends 'auth/base_auth.html' %}

{% block title %}Create user account{% endblock %} 

{% block content %}
<body class="min-h-screen">

    <!-- Header with Logo and Home Icon -->
    <header class="absolute top-0 left-0 right-0 p-6 flex justify-between items-center z-20">
        <!-- Logo -->
        <div class="flex items-center">
            <svg width="28" height="28" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16C0 24.8366 7.16344 32 16 32Z" fill="#007BFF"/>
                <path d="M22.5 14.5L12.5 20.5L16.5 11.5L22.5 14.5Z" fill="white"/>
            </svg>
            <span class="ml-2 text-2xl font-bold" style="color: #007BFF;">VibeReach</span>
        </div>
        <!-- Home Icon -->
        <a href="#" class="text-gray-600 hover:text-gray-900">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
        </a>
    </header>

    <main class="min-h-screen grid grid-cols-1 lg:grid-cols-2 items-center">
        
        <!-- Left Column: Sign-up Form -->
        <div class="flex flex-col items-center justify-center p-8 lg:p-12 w-full mt-20 lg:mt-0">
            <div class="w-full max-w-md">
                <h1 class="text-3xl font-bold mb-6">Create a new account</h1>
                
                <form class="space-y-4">
                    <!-- First and Last Name -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="form-control">
                            <input type="text" placeholder="First Name" class="input input-bordered bg-white" required />
                        </div>
                        <div class="form-control">
                            <input type="text" placeholder="Last Name" class="input input-bordered bg-white" required />
                        </div>
                    </div>
                    
                    <!-- Email -->
                    <div class="form-control">
                        <input type="email" placeholder="Email" class="input input-bordered bg-white" required />
                    </div>

                    <!-- Password -->
                    <div class="form-control">
                        <input type="password" placeholder="Password" class="input input-bordered bg-white" required />
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="form-control">
                        <label class="label cursor-pointer justify-start gap-3">
                            <input type="checkbox" class="checkbox checkbox-sm" />
                            <span class="label-text">I agree to the VibeReach <a href="#" class="link link-primary-custom">Terms of Use</a> and <a href="#" class="link link-primary-custom">Privacy policy</a>.</span>
                        </label>
                    </div>

                    <!-- Join Now Button -->
                    <div class="form-control pt-2">
                        <button class="btn btn-primary-custom h-12 text-lg rounded-lg">Join Now</button>
                    </div>

                    <!-- Divider -->
                    <div class="divider my-4">OR</div>

                    <!-- Social Sign Up -->
                    <div class="space-y-3">
                        <button type="button" class="btn btn-outline bg-white w-full h-12 rounded-lg border-gray-300 hover:bg-gray-50 hover:border-gray-400">
                             <img src="https://www.google.com/favicon.ico" alt="Google" class="w-5 h-5 mr-2">
                            Sign Up with Google
                        </button>
                         <button type="button" class="btn btn-outline bg-white w-full h-12 rounded-lg border-gray-300 hover:bg-gray-50 hover:border-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
                               <path d="M17.252 12.01c-.01-2.288 1.93-3.05 1.98-3.074.008-.003-1.605-.96-3.95-1.022-3.435-.04-5.276 2.004-6.68 2.004-1.392 0-3.034-1.923-4.992-1.89-2.21.034-3.882 1.25-4.832 3.123-2.07 4.098-.52 9.94 1.764 13.23 1.134 1.61 2.45 3.39 4.162 3.34 1.63-.048 2.21-1.05 4.192-1.05 1.982 0 2.502 1.05 4.232 1.023 1.78-.027 2.94-1.57 4.02-3.216 1.3-1.98 1.83-3.87 1.88-3.92-.04-.02-3.41-1.33-3.48-5.57zM14.75 6.742c.98-.992 1.57-2.37 1.41-3.75-.98.05-2.26.6-3.24 1.59-.89.9-1.63 2.29-1.47 3.65.98-.03 2.32-.51 3.3-1.49z"/>
                            </svg>
                            Sign Up with Apple
                        </button>
                    </div>

                     <!-- Log In Link -->
                    <div class="text-center pt-4">
                        <p class="text-sm">Already have an account? <a href="#" class="font-bold link text-black">Log in</a></p>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Right Column: Promotional Content -->
        <div class="hidden lg:flex relative w-full h-full items-center justify-center">
            <div class="wave-container">
                <div class="wave"></div>
            </div>
            <div class="relative z-10 text-center max-w-md p-8">
                <!-- Illustration SVG -->
                <svg viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg" class="w-64 h-auto mx-auto mb-8">
                    <!-- A simplified representation of the illustration -->
                    <g fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <!-- Person -->
                        <path d="M120,125 C110,135 90,135 80,125" stroke="#374151" stroke-width="2"/>
                        <circle cx="100" cy="80" r="15" fill="#E5E7EB"/>
                        <path d="M100,95 v20" stroke="#374151" stroke-width="2"/>
                        <path d="M80,115 h40" stroke="#374151" stroke-width="2"/>
                        <path d="M90,70 A10 10 0 0 1 110 70" stroke="#374151" stroke-width="2" fill="#fff"/>
                         <path d="M100,80 C 120,60 140,75 150,90" stroke="#374151" stroke-width="3"/>
                        
                        <!-- Laptop -->
                        <rect x="40" y="100" width="60" height="35" rx="5" fill="#D1D5DB"/>
                        <rect x="35" y="135" width="70" height="5" rx="2" fill="#9CA3AF"/>
                        <circle cx="55" cy="118" r="5" stroke="#fff" stroke-width="1.5" fill="#60A5FA"/>

                        <!-- UI Elements -->
                         <rect x="130" y="40" width="50" height="60" rx="5" fill="#fff" stroke="#9CA3AF" stroke-width="1"/>
                         <path d="M135,50 h40 M135,60 h30 M135,70 h35 M135,80 h20" stroke="#374151" stroke-width="1.5"/>
                         <circle cx="137" cy="45" r="1.5" fill="#F87171"/>
                         <circle cx="144" cy="45" r="1.5" fill="#FBBF24"/>
                         <circle cx="151" cy="45" r="1.5" fill="#34D399"/>

                        <!-- Floating elements & lines -->
                        <path d="M80,60 Q60,70 80,80" stroke="#60A5FA" stroke-width="2" />
                        <path d="M120,50 L80,90" stroke="#60A5FA" stroke-width="1.5" stroke-dasharray="3 3"/>
                    </g>
                </svg>

                <h2 class="text-3xl font-bold text-gray-800">30,000+ clients<br>are getting more replies!</h2>
                <p class="mt-4 text-gray-600">
                    Unlock the power of effective outreach with our cutting-edge platform, and experience a surge in responses and engagement rates like never before.
                </p>
            </div>
        </div>

    </main>

</body>

{% endblock %}48-
