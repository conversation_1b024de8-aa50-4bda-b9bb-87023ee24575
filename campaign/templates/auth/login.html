{% extends 'auth/base_auth.html' %}

{% block title %}Login{% endblock %} 

{% block content %}
<body class="min-h-screen">

    <!-- Header with Home Icon -->
    <header class="absolute top-0 right-0 p-6">
        <a href="#" class="text-gray-600 hover:text-gray-900">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
        </a>
    </header>

    <main class="min-h-screen flex flex-col items-center justify-center p-4">
        
        <!-- Logo -->
        <div class="flex items-center mb-6">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16C0 24.8366 7.16344 32 16 32Z" fill="#4285F4"/>
                <path d="M22.5 14.5L12.5 20.5L16.5 11.5L22.5 14.5Z" fill="white"/>
            </svg>
            <span class="ml-3 text-3xl font-bold" style="color: #4285F4;">VibeReach</span>
        </div>

        <!-- Login Card -->
        <div class="card w-full max-w-sm shrink-0 shadow-lg bg-base-100">
            <form class="card-body"
                  hx-post="{% url 'api_login' %}"
                  hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                  hx-indicator="#login-loading">
                {% csrf_token %}
                <h2 class="card-title text-2xl font-bold justify-start mb-4">Login</h2>

                <!-- Error Messages -->
                <div id="login-errors" class="hidden">
                    <div class="alert alert-error mb-4">
                        <span id="error-message"></span>
                    </div>
                </div>

                <!-- Email Input -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Email</span>
                    </label>
                    <input type="email" name="email" placeholder="" class="input input-bordered" required />
                    <label class="label">
                        <span class="label-text-alt text-red-500 hidden" id="email-error">Enter a valid email address</span>
                    </label>
                </div>

                <!-- Password Input -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Password</span>
                    </label>
                    <input type="password" name="password" placeholder="" class="input input-bordered" required />
                    <label class="label">
                        <span class="label-text-alt text-red-500 hidden" id="password-error">Password is required</span>
                    </label>
                </div>

                <!-- Login Button and Forgot Password -->
                <div class="form-control mt-6 flex flex-row items-center justify-between">
                     <button type="submit" class="btn btn-success w-32" style="background-color: #27C464; border-color: #27C464; color: white;">
                        <span class="loading loading-spinner loading-sm hidden" id="login-loading"></span>
                        Log In
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                           <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                    </button>
                    <a href="#" class="label-text-alt link link-hover">Forgot password?</a>
                </div>

                <!-- Divider -->
                <div class="divider my-4">OR</div>

                <!-- Social Logins -->
                <div class="form-control">
                    <button class="btn btn-outline bg-white border-gray-300 hover:bg-gray-50 hover:border-gray-400">
                        <img src="https://www.google.com/favicon.ico" alt="Google" class="w-4 h-4 mr-2">
                        Log In with Google
                    </button>
                </div>
                <div class="form-control mt-2">
                     <button class="btn btn-outline bg-white border-gray-300 hover:bg-gray-50 hover:border-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                           <path d="M17.252 12.01c-.01-2.288 1.93-3.05 1.98-3.074.008-.003-1.605-.96-3.95-1.022-3.435-.04-5.276 2.004-6.68 2.004-1.392 0-3.034-1.923-4.992-1.89-2.21.034-3.882 1.25-4.832 3.123-2.07 4.098-.52 9.94 1.764 13.23 1.134 1.61 2.45 3.39 4.162 3.34 1.63-.048 2.21-1.05 4.192-1.05 1.982 0 2.502 1.05 4.232 1.023 1.78-.027 2.94-1.57 4.02-3.216 1.3-1.98 1.83-3.87 1.88-3.92-.04-.02-3.41-1.33-3.48-5.57zM14.75 6.742c.98-.992 1.57-2.37 1.41-3.75-.98.05-2.26.6-3.24 1.59-.89.9-1.63 2.29-1.47 3.65.98-.03 2.32-.51 3.3-1.49z"/>
                        </svg>
                        Log In with Apple
                    </button>
                </div>

                <!-- Sign Up Link -->
                <div class="text-center mt-4">
                    <p class="text-sm">Don't have an account? <a href="#" class="font-bold link text-black">Sign Up</a></p>
                </div>
            </form>
        </div>
    </main>

    <script>
        // Handle HTMX response for login
        document.body.addEventListener('htmx:afterRequest', function(event) {
            if (event.detail.target.tagName === 'FORM') {
                const response = JSON.parse(event.detail.xhr.responseText);

                if (response.success) {
                    // Redirect on success
                    window.location.href = response.redirect_url;
                } else {
                    // Show errors
                    const errorDiv = document.getElementById('login-errors');
                    const errorMessage = document.getElementById('error-message');

                    if (response.errors) {
                        let errorText = '';
                        if (response.errors.email) {
                            errorText += response.errors.email.join(', ') + ' ';
                            document.getElementById('email-error').classList.remove('hidden');
                        }
                        if (response.errors.password) {
                            errorText += response.errors.password.join(', ') + ' ';
                            document.getElementById('password-error').classList.remove('hidden');
                        }
                        if (response.errors.non_field_errors) {
                            errorText += response.errors.non_field_errors.join(', ');
                        }
                        errorMessage.textContent = errorText || 'Login failed. Please try again.';
                    } else {
                        errorMessage.textContent = 'Login failed. Please try again.';
                    }

                    errorDiv.classList.remove('hidden');
                }
            }
        });

        // Clear errors when user starts typing
        document.addEventListener('input', function(event) {
            if (event.target.name === 'email') {
                document.getElementById('email-error').classList.add('hidden');
            }
            if (event.target.name === 'password') {
                document.getElementById('password-error').classList.add('hidden');
            }
            document.getElementById('login-errors').classList.add('hidden');
        });
    </script>
</body>

{% endblock %}